'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Template, Brand } from '@/types';

export default function TemplateDetail() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const templateId = params.id as string;
  
  const [template, setTemplate] = useState<Template | null>(null);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [loadingData, setLoadingData] = useState(true);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user && templateId) {
      fetchTemplateData();
    }
  }, [user, templateId]);

  const fetchTemplateData = async () => {
    try {
      setLoadingData(true);

      // Fetch template
      const templateDoc = await getDoc(doc(db, 'templates', templateId));
      if (templateDoc.exists()) {
        const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;
        setTemplate(templateData);

        // Fetch brand
        const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));
        if (brandDoc.exists()) {
          setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);
        }
      } else {
        router.push('/templates');
      }

    } catch (error) {
      console.error('Error fetching template data:', error);
      router.push('/templates');
    } finally {
      setLoadingData(false);
    }
  };

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!user || !template) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-xl font-semibold text-gray-900">
                VALTICS AI
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/templates"
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                ← Back to Templates
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Template Header */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-8">
              {/* Brand info */}
              {brand && (
                <div className="flex items-center mb-6">
                  {brand.logoUrl && (
                    <img
                      src={brand.logoUrl}
                      alt={brand.name}
                      className="h-8 w-auto mr-3"
                    />
                  )}
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">{brand.name}</h2>
                    <p className="text-sm text-gray-500">{brand.description}</p>
                  </div>
                </div>
              )}

              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-gray-900 mb-4">
                    {template.name}
                  </h1>
                  
                  <p className="text-lg text-gray-600 mb-6">
                    {template.description}
                  </p>

                  <div className="flex items-center space-x-4 mb-6">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      {template.category}
                    </span>
                    <span className="text-2xl font-bold text-green-600">
                      ${template.price}
                    </span>
                  </div>
                </div>
              </div>

              {/* Available Formats */}
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Available Formats</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {template.fileUrls.pdf && (
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center mr-3">
                          <span className="text-white text-xs font-bold">PDF</span>
                        </div>
                        <span className="font-medium">PDF Document</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">
                        Professional presentation format, perfect for client meetings and executive reviews.
                      </p>
                      <a
                        href={template.fileUrls.pdf}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Preview PDF →
                      </a>
                    </div>
                  )}

                  {template.fileUrls.ppt && (
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mr-3">
                          <span className="text-white text-xs font-bold">PPT</span>
                        </div>
                        <span className="font-medium">PowerPoint</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">
                        Editable presentation slides for customization and interactive presentations.
                      </p>
                      <a
                        href={template.fileUrls.ppt}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download PPT →
                      </a>
                    </div>
                  )}

                  {template.fileUrls.excel && (
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center mr-3">
                          <span className="text-white text-xs font-bold">XLS</span>
                        </div>
                        <span className="font-medium">Excel Workbook</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">
                        Detailed calculations and data analysis with formulas and charts.
                      </p>
                      <a
                        href={template.fileUrls.excel}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download Excel →
                      </a>
                    </div>
                  )}
                </div>
              </div>

              {/* Template Features */}
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">What's Included</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Executive Summary Template</h4>
                      <p className="text-sm text-gray-600">Pre-formatted executive summary with key metrics</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">ROI Calculations</h4>
                      <p className="text-sm text-gray-600">Automated ROI and payback period calculations</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Visual Charts & Graphs</h4>
                      <p className="text-sm text-gray-600">Professional charts for data visualization</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Customizable Sections</h4>
                      <p className="text-sm text-gray-600">Flexible sections for your specific use case</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <Link
                  href={`/bva/new?template=${template.id}`}
                  className="bg-blue-600 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Start BVA with this Template
                </Link>
                <Link
                  href="/templates"
                  className="bg-gray-200 text-gray-800 px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-300 transition-colors"
                >
                  Browse Other Templates
                </Link>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Template Information</h3>
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="text-sm text-gray-900">
                    {new Date(template.createdAt).toLocaleDateString()}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                  <dd className="text-sm text-gray-900">
                    {new Date(template.updatedAt).toLocaleDateString()}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Category</dt>
                  <dd className="text-sm text-gray-900">{template.category}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Brand</dt>
                  <dd className="text-sm text-gray-900">{brand?.name}</dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
