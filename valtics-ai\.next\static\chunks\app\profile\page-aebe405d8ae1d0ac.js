(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{1138:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>n});var r=a(3915),s=a(6203),d=a(5317),l=a(858);let i=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),n=(0,s.xI)(i),c=(0,d.aU)(i);(0,l.c7)(i)},1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return n},getImageProps:function(){return i}});let r=a(8229),s=a(8883),d=a(3063),l=r._(a(1193));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let n=d.Image},1573:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5155),s=a(6874),d=a.n(s),l=a(6766),i=a(3274),n=a(5695),c=a(3074);function o(e){let{title:t="VALTICS AI",showBackButton:a=!1,backUrl:s="/dashboard",backText:o="← Back to Dashboard"}=e,{user:m,logOut:x,isAdmin:u}=(0,i.A)(),g=(0,n.useRouter)(),h=async()=>{try{await x(),g.push("/")}catch(e){console.error("Error logging out:",e)}};return m?(0,r.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(d(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,r.jsx)(l.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,r.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:t})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[a&&(0,r.jsx)(d(),{href:s,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:o}),!a&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,r.jsx)(d(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),u&&(0,r.jsx)(d(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,r.jsx)(d(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,r.jsx)(c.default,{}),(0,r.jsx)("button",{onClick:h,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},3074:(e,t,a)=>{"use strict";a.d(t,{default:()=>l});var r=a(5155),s=a(3391),d=a(2115);function l(){let[e,t]=(0,d.useState)(!1),[a,l]=(0,d.useState)("light"),i=(0,s.Q)();(0,d.useEffect)(()=>{if(t(!0),!i){let e=localStorage.getItem("theme");if(e)l(e),n(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";l(e),n(e)}}},[i]);let n=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};if(!e)return(0,r.jsx)("div",{className:"p-2 w-9 h-9"});let c=i?i.theme:a;return(0,r.jsx)("button",{onClick:()=>{if(i)i.toggleTheme();else{let e="light"===a?"dark":"light";l(e),n(e),localStorage.setItem("theme",e)}},className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":"Switch to ".concat("light"===c?"dark":"light"," mode"),title:"Switch to ".concat("light"===c?"dark":"light"," mode"),children:"light"===c?(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}},3274:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>c});var r=a(5155),s=a(2115),d=a(6203),l=a(1138),i=a(5317);let n=(0,s.createContext)(null),c=e=>{let{children:t}=e,[a,c]=(0,s.useState)(null),[o,m]=(0,s.useState)(!0),[x,u]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=(0,d.hg)(l.j2,async e=>{if(c(e),e){var t;u((null==(t=(await (0,i.x7)((0,i.H9)(l.db,"users",e.uid))).data())?void 0:t.role)==="admin")}else u(!1);m(!1)});return()=>e()},[]);let g=async(e,t)=>{await (0,d.x9)(l.j2,e,t)},h=async(e,t)=>{let a=await (0,d.eJ)(l.j2,e,t);await (0,i.BN)((0,i.H9)(l.db,"users",a.user.uid),{email:a.user.email,role:"user",createdAt:new Date})},b=async()=>{await (0,d.CI)(l.j2)},y=async()=>{let e=new d.HF,t=await (0,d.df)(l.j2,e);(await (0,i.x7)((0,i.H9)(l.db,"users",t.user.uid))).exists()||await (0,i.BN)((0,i.H9)(l.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},p=async()=>{let e=new d.sk,t=await (0,d.df)(l.j2,e);(await (0,i.x7)((0,i.H9)(l.db,"users",t.user.uid))).exists()||await (0,i.BN)((0,i.H9)(l.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,r.jsx)(n.Provider,{value:{user:a,loading:o,signIn:g,signUp:h,logOut:b,signInWithGoogle:y,signInWithFacebook:p,isAdmin:x},children:t})},o=()=>{let e=(0,s.useContext)(n);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},3391:(e,t,a)=>{"use strict";a.d(t,{Q:()=>l,ThemeProvider:()=>i});var r=a(5155),s=a(2115);let d=(0,s.createContext)(null),l=()=>(0,s.useContext)(d),i=e=>{let{children:t}=e,[a,l]=(0,s.useState)("light"),[i,n]=(0,s.useState)(!1);(0,s.useEffect)(()=>{n(!0);let e=localStorage.getItem("theme");if(e)l(e),c(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";l(e),c(e)}},[]);let c=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return i?(0,r.jsx)(d.Provider,{value:{theme:a,toggleTheme:()=>{let e="light"===a?"dark":"light";l(e),c(e),localStorage.setItem("theme",e)}},children:t}):(0,r.jsx)(r.Fragment,{children:t})}},3581:(e,t,a)=>{Promise.resolve().then(a.bind(a,8591))},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>s.a});var r=a(1469),s=a.n(r)},8591:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),s=a(2115),d=a(3274),l=a(5695),i=a(6874),n=a.n(i),c=a(5317),o=a(1138),m=a(1573);function x(){let{user:e,loading:t,logOut:a}=(0,d.A)(),i=(0,l.useRouter)(),[x,u]=(0,s.useState)(null),[g,h]=(0,s.useState)(null),[b,y]=(0,s.useState)([]),[p,f]=(0,s.useState)(!0),[j,v]=(0,s.useState)("profile"),[N,w]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t||e||i.push("/login")},[e,t,i]),(0,s.useEffect)(()=>{e&&k()},[e]);let k=async()=>{try{f(!0);let t=await (0,c.x7)((0,c.H9)(o.db,"users",e.uid));t.exists()&&u({id:t.id,...t.data()});let a=(0,c.P)((0,c.rJ)(o.db,"subscriptions"),(0,c._M)("userId","==",e.uid)),r=await (0,c.GG)(a);if(!r.empty){let e=r.docs[0];h({id:e.id,...e.data()})}let s=(0,c.P)((0,c.rJ)(o.db,"bvaInstances"),(0,c._M)("userId","==",e.uid),(0,c.My)("updatedAt","desc")),d=(await (0,c.GG)(s)).docs.map(e=>({id:e.id,...e.data()}));y(d)}catch(e){console.error("Error fetching profile data:",e)}finally{f(!1)}},S=async()=>{try{await a(),i.push("/")}catch(e){console.error("Error logging out:",e)}};if(t||p)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"text-xl text-gray-900 dark:text-white",children:"Loading..."})});if(!e||!x)return null;let A=e=>{switch(e){case"completed":return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";case"in-progress":return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(m.A,{title:"VALTICS AI",showBackButton:!0}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Profile & Settings"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Manage your account settings and view your activity."})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("nav",{className:"flex space-x-8",children:[{id:"profile",name:"Profile"},{id:"subscription",name:"Subscription"},{id:"bvas",name:"My BVAs"}].map(e=>(0,r.jsx)("button",{onClick:()=>v(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(j===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"),children:e.name},e.id))})}),"profile"===j&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,r.jsx)("input",{type:"email",value:e.email||"",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Email cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Account Type"}),(0,r.jsx)("input",{type:"text",value:"admin"===x.role?"Administrator":"Standard User",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Member Since"}),(0,r.jsx)("input",{type:"text",value:new Date(x.createdAt).toLocaleDateString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Total BVAs Created"}),(0,r.jsx)("input",{type:"text",value:b.length.toString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Account Actions"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Change Password"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Update your account password"})]}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"Change Password"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Download Data"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Export all your BVA data"})]}),(0,r.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700",children:"Export Data"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-red-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-red-900",children:"Sign Out"}),(0,r.jsx)("p",{className:"text-sm text-red-600",children:"Sign out of your account"})]}),(0,r.jsx)("button",{onClick:S,className:"bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700",children:"Sign Out"})]})]})]})})]}),"subscription"===j&&(0,r.jsx)("div",{className:"space-y-6",children:g?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Current Subscription"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan Type"}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("enterprise"===g.type?"bg-purple-100 text-purple-800":"premium"===g.type?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:g.type.charAt(0).toUpperCase()+g.type.slice(1)})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("active"===g.status?"bg-green-100 text-green-800":"cancelled"===g.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:g.status.charAt(0).toUpperCase()+g.status.slice(1)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:new Date(g.startDate).toLocaleDateString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:new Date(g.endDate).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Template Access"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900",children:[g.accessibleTemplateIds.length," templates available"]})]})]}),(0,r.jsxs)("div",{className:"mt-6 flex space-x-4",children:[(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"Upgrade Plan"}),(0,r.jsx)("button",{className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400",children:"Manage Billing"})]})]})}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCB3"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Active Subscription"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Subscribe to access premium templates and features."}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700",children:"View Subscription Plans"})]})})}),"bvas"===j&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Your BVAs"}),b.length>0?(0,r.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Client: ",e.clientName]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:["Last updated: ",new Date(e.updatedAt).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(A(e.status)),children:e.status}),(0,r.jsx)(n(),{href:"/bva/".concat(e.id),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View"})]})]})},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\uD83D\uDCCA"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No BVAs created yet."}),(0,r.jsx)(n(),{href:"/bva/new",className:"mt-4 inline-block bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"Create Your First BVA"})]})]})})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,288,874,63,441,684,358],()=>t(3581)),_N_E=e.O()}]);