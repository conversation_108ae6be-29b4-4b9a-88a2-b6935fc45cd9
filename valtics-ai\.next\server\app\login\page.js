(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29903:(e,t,r)=>{Promise.resolve().then(r.bind(r,46387))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37148:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46387)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\login\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},38151:(e,t,r)=>{Promise.resolve().then(r.bind(r,46501))},40303:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o,ThemeProvider:()=>n});var s=r(60687),a=r(43210);let i=(0,a.createContext)(null),o=()=>(0,a.useContext)(i),n=({children:e})=>{let[t,r]=(0,a.useState)("light"),[o,n]=(0,a.useState)(!1);(0,a.useEffect)(()=>{n(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return o?(0,s.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},46387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\login\\page.tsx","default")},46501:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),i=r(51108),o=r(16189),n=r(85814),l=r.n(n);function d(){let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)(""),[d,u]=(0,a.useState)(""),{signIn:c,signInWithGoogle:m,signInWithFacebook:h}=(0,i.A)(),p=(0,o.useRouter)(),v=async t=>{t.preventDefault(),u("");try{await c(e,r),p.push("/dashboard")}catch(e){u(e.message)}},x=async()=>{try{await m(),p.push("/dashboard")}catch(e){u(e.message)}},b=async()=>{try{await h(),p.push("/dashboard")}catch(e){u(e.message)}};return(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24 bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Login to VALTICS AI"})}),d&&(0,s.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:d}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:r,onChange:e=>n(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Sign in"})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400",children:"Or continue with"})})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsx)("button",{onClick:x,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Google"}),(0,s.jsx)("button",{onClick:b,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Facebook"})]})]}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Don't have an account?"," ",(0,s.jsx)(l(),{href:"/register",className:"font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300",children:"Register"})]})})]})})}},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,AuthProvider:()=>d});var s=r(60687),a=r(43210),i=r(19978),o=r(56304),n=r(75535);let l=(0,a.createContext)(null),d=({children:e})=>{let[t,r]=(0,a.useState)(null),[d,u]=(0,a.useState)(!0),[c,m]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=(0,i.hg)(o.j2,async e=>{if(r(e),e){let t=await (0,n.x7)((0,n.H9)(o.db,"users",e.uid));m(t.data()?.role==="admin")}else m(!1);u(!1)});return()=>e()},[]);let h=async(e,t)=>{await (0,i.x9)(o.j2,e,t)},p=async(e,t)=>{let r=await (0,i.eJ)(o.j2,e,t);await (0,n.BN)((0,n.H9)(o.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},v=async()=>{await (0,i.CI)(o.j2)},x=async()=>{let e=new i.HF,t=await (0,i.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},b=async()=>{let e=new i.sk,t=await (0,i.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(l.Provider,{value:{user:t,loading:d,signIn:h,signUp:p,logOut:v,signInWithGoogle:x,signInWithFacebook:b,isAdmin:c},children:e})},u=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>l});var s=r(67989),a=r(19978),i=r(75535),o=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,a.xI)(n),d=(0,i.aU)(n);(0,o.c7)(n)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),a=r(61421),i=r.n(a);r(82704);var o=r(94442),n=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:i().className,children:(0,s.jsx)(n.ThemeProvider,{children:(0,s.jsx)(o.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,567],()=>r(37148));module.exports=s})();