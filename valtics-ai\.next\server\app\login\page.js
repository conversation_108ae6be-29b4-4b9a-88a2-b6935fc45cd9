(()=>{var e={};e.id=520,e.ids=[520],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1700:(e,t,r)=>{Promise.resolve().then(r.bind(r,4442))},1820:e=>{"use strict";e.exports=require("os")},1861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},2264:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>m,A:()=>h});var s=r(687),i=r(3210),a=r(9978),o=r(7989),n=r(5535),l=r(146);let d=(0,o.Dk)().length?(0,o.Sx)():(0,o.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),u=(0,a.xI)(d),c=(0,n.aU)(d);(0,l.c7)(d);let p=(0,i.createContext)(null),m=({children:e})=>{let[t,r]=(0,i.useState)(null),[o,l]=(0,i.useState)(!0),[d,m]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=(0,a.hg)(u,async e=>{if(r(e),e){let t=await (0,n.x7)((0,n.H9)(c,"users",e.uid));m(t.data()?.role==="admin")}else m(!1);l(!1)});return()=>e()},[]);let h=async(e,t)=>{await (0,a.x9)(u,e,t)},x=async(e,t)=>{let r=await (0,a.eJ)(u,e,t);await (0,n.BN)((0,n.H9)(c,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},v=async()=>{await (0,a.CI)(u)},b=async()=>{let e=new a.HF,t=await (0,a.df)(u,e);(await (0,n.x7)((0,n.H9)(c,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(c,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},f=async()=>{let e=new a.sk,t=await (0,a.df)(u,e);(await (0,n.x7)((0,n.H9)(c,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(c,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(p.Provider,{value:{user:t,loading:o,signIn:h,signUp:x,logOut:v,signInWithGoogle:b,signInWithFacebook:f,isAdmin:d},children:e})},h=()=>{let e=(0,i.useContext)(p);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3496:e=>{"use strict";e.exports=require("http2")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(2907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4985:e=>{"use strict";e.exports=require("dns")},5511:e=>{"use strict";e.exports=require("crypto")},6055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\login\\page.tsx","default")},7148:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),o=r.n(a),n=r(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6387)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\login\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7732:(e,t,r)=>{Promise.resolve().then(r.bind(r,2264))},7781:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(687),i=r(3210),a=r(2264),o=r(5773),n=r(5814),l=r.n(n);function d(){let[e,t]=(0,i.useState)(""),[r,n]=(0,i.useState)(""),[d,u]=(0,i.useState)(""),{signIn:c,signInWithGoogle:p,signInWithFacebook:m}=(0,a.A)(),h=(0,o.useRouter)(),x=async t=>{t.preventDefault(),u("");try{await c(e,r),h.push("/dashboard")}catch(e){u(e.message)}},v=async()=>{try{await p(),h.push("/dashboard")}catch(e){u(e.message)}},b=async()=>{try{await m(),h.push("/dashboard")}catch(e){u(e.message)}};return(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Login to VALTICS AI"})}),d&&(0,s.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:d}),(0,s.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:r,onChange:e=>n(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Sign in"})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsx)("button",{onClick:v,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Google"}),(0,s.jsx)("button",{onClick:b,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Facebook"})]})]}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(l(),{href:"/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"Register"})]})})]})})}},7910:e=>{"use strict";e.exports=require("stream")},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n});var s=r(7413),i=r(1421),a=r.n(i);r(2704);var o=r(4442);let n={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(o.AuthProvider,{children:e})})})}},8151:(e,t,r)=>{Promise.resolve().then(r.bind(r,7781))},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9771:e=>{"use strict";e.exports=require("process")},9903:(e,t,r)=>{Promise.resolve().then(r.bind(r,6387))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,658,814],()=>r(7148));module.exports=s})();