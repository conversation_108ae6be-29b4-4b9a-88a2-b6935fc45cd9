{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/bva/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState, Suspense } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { doc, getDoc, collection, addDoc } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Template, Brand, BVAFormData } from '@/types';\n\nfunction NewBVAContent() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const templateId = searchParams.get('template');\n\n  const [template, setTemplate] = useState<Template | null>(null);\n  const [brand, setBrand] = useState<Brand | null>(null);\n  const [loadingData, setLoadingData] = useState(true);\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState<BVAFormData>({\n    clientName: '',\n    projectName: '',\n    industry: '',\n    currentCosts: 0,\n    expectedBenefits: 0,\n    timeframe: 12,\n    additionalData: {}\n  });\n  const [saving, setSaving] = useState(false);\n\n  const totalSteps = 4;\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user && templateId) {\n      fetchTemplateData();\n    } else if (user && !templateId) {\n      setLoadingData(false);\n    }\n  }, [user, templateId]);\n\n  const fetchTemplateData = async () => {\n    try {\n      setLoadingData(true);\n\n      if (templateId) {\n        // Fetch template\n        const templateDoc = await getDoc(doc(db, 'templates', templateId));\n        if (templateDoc.exists()) {\n          const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;\n          setTemplate(templateData);\n\n          // Fetch brand\n          const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));\n          if (brandDoc.exists()) {\n            setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Error fetching template data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleInputChange = (field: keyof BVAFormData, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleNext = () => {\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleSave = async (status: 'draft' | 'in-progress' = 'draft') => {\n    if (!user) return;\n\n    try {\n      setSaving(true);\n\n      const bvaData = {\n        userId: user.uid,\n        templateId: templateId || '',\n        name: formData.projectName || 'Untitled BVA',\n        clientName: formData.clientName,\n        status,\n        data: formData,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      const docRef = await addDoc(collection(db, 'bvaInstances'), bvaData);\n      router.push(`/bva/${docRef.id}`);\n\n    } catch (error) {\n      console.error('Error saving BVA:', error);\n      alert('Error saving BVA. Please try again.');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Project Information</h2>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Project Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.projectName}\n                onChange={(e) => handleInputChange('projectName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter project name\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Client Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.clientName}\n                onChange={(e) => handleInputChange('clientName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter client name\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Industry\n              </label>\n              <select\n                value={formData.industry}\n                onChange={(e) => handleInputChange('industry', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"\">Select industry</option>\n                <option value=\"Technology\">Technology</option>\n                <option value=\"Healthcare\">Healthcare</option>\n                <option value=\"Finance\">Finance</option>\n                <option value=\"Manufacturing\">Manufacturing</option>\n                <option value=\"Retail\">Retail</option>\n                <option value=\"Education\">Education</option>\n                <option value=\"Government\">Government</option>\n                <option value=\"Other\">Other</option>\n              </select>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Financial Information</h2>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Current Annual Costs ($)\n              </label>\n              <input\n                type=\"number\"\n                value={formData.currentCosts}\n                onChange={(e) => handleInputChange('currentCosts', parseFloat(e.target.value) || 0)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"0\"\n                min=\"0\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Current annual costs related to this solution area\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Expected Annual Benefits ($)\n              </label>\n              <input\n                type=\"number\"\n                value={formData.expectedBenefits}\n                onChange={(e) => handleInputChange('expectedBenefits', parseFloat(e.target.value) || 0)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"0\"\n                min=\"0\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Expected annual benefits from the new solution\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Analysis Timeframe (months)\n              </label>\n              <select\n                value={formData.timeframe}\n                onChange={(e) => handleInputChange('timeframe', parseInt(e.target.value))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value={12}>12 months</option>\n                <option value={24}>24 months</option>\n                <option value={36}>36 months</option>\n                <option value={48}>48 months</option>\n                <option value={60}>60 months</option>\n              </select>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Additional Details</h2>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Key Business Drivers\n              </label>\n              <textarea\n                value={formData.additionalData.businessDrivers || ''}\n                onChange={(e) => handleInputChange('additionalData', {\n                  ...formData.additionalData,\n                  businessDrivers: e.target.value\n                })}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Describe the key business drivers for this project...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Success Metrics\n              </label>\n              <textarea\n                value={formData.additionalData.successMetrics || ''}\n                onChange={(e) => handleInputChange('additionalData', {\n                  ...formData.additionalData,\n                  successMetrics: e.target.value\n                })}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Define how success will be measured...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Implementation Timeline\n              </label>\n              <input\n                type=\"text\"\n                value={formData.additionalData.timeline || ''}\n                onChange={(e) => handleInputChange('additionalData', {\n                  ...formData.additionalData,\n                  timeline: e.target.value\n                })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"e.g., 6 months implementation, 3 months rollout\"\n              />\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Review & Save</h2>\n\n            <div className=\"bg-gray-50 rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">BVA Summary</h3>\n\n              <dl className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Project Name</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.projectName}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Client</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.clientName}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Industry</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.industry || 'Not specified'}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Timeframe</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.timeframe} months</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Current Costs</dt>\n                  <dd className=\"text-sm text-gray-900\">${formData.currentCosts.toLocaleString()}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Expected Benefits</dt>\n                  <dd className=\"text-sm text-gray-900\">${formData.expectedBenefits.toLocaleString()}</dd>\n                </div>\n              </dl>\n\n              {template && (\n                <div className=\"mt-4 pt-4 border-t\">\n                  <dt className=\"text-sm font-medium text-gray-500\">Template</dt>\n                  <dd className=\"text-sm text-gray-900\">{template.name}</dd>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() => handleSave('draft')}\n                disabled={saving}\n                className=\"flex-1 bg-gray-600 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-700 disabled:opacity-50\"\n              >\n                {saving ? 'Saving...' : 'Save as Draft'}\n              </button>\n              <button\n                onClick={() => handleSave('in-progress')}\n                disabled={saving}\n                className=\"flex-1 bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {saving ? 'Saving...' : 'Save & Continue'}\n              </button>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-xl font-semibold text-gray-900\">\n                VALTICS AI\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Cancel\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-3xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Create New BVA</h1>\n            {template && brand && (\n              <p className=\"mt-2 text-gray-600\">\n                Using template: <span className=\"font-medium\">{template.name}</span> by {brand.name}\n              </p>\n            )}\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center\">\n              {Array.from({ length: totalSteps }, (_, i) => (\n                <div key={i} className=\"flex items-center\">\n                  <div\n                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                      i + 1 <= currentStep\n                        ? 'bg-blue-600 text-white'\n                        : 'bg-gray-200 text-gray-600'\n                    }`}\n                  >\n                    {i + 1}\n                  </div>\n                  {i < totalSteps - 1 && (\n                    <div\n                      className={`w-16 h-1 mx-2 ${\n                        i + 1 < currentStep ? 'bg-blue-600' : 'bg-gray-200'\n                      }`}\n                    />\n                  )}\n                </div>\n              ))}\n            </div>\n            <div className=\"flex justify-between mt-2 text-sm text-gray-600\">\n              <span>Project Info</span>\n              <span>Financial</span>\n              <span>Details</span>\n              <span>Review</span>\n            </div>\n          </div>\n\n          {/* Form Content */}\n          <div className=\"bg-white rounded-lg shadow-md p-8 mb-8\">\n            {renderStep()}\n          </div>\n\n          {/* Navigation Buttons */}\n          {currentStep < totalSteps && (\n            <div className=\"flex justify-between\">\n              <button\n                onClick={handlePrevious}\n                disabled={currentStep === 1}\n                className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Previous\n              </button>\n              <button\n                onClick={handleNext}\n                disabled={\n                  (currentStep === 1 && (!formData.projectName || !formData.clientName)) ||\n                  saving\n                }\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Next\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function NewBVA() {\n  return (\n    <Suspense fallback={\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    }>\n      <NewBVAContent />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AAUA,SAAS;IACP,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,aAAa,aAAa,GAAG,CAAC;IAEpC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QACpD,YAAY;QACZ,aAAa;QACb,UAAU;QACV,cAAc;QACd,kBAAkB;QAClB,WAAW;QACX,gBAAgB,CAAC;IACnB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,YAAY;YACtB;QACF,OAAO,IAAI,QAAQ,CAAC,YAAY;YAC9B,eAAe;QACjB;IACF,GAAG;QAAC;QAAM;KAAW;IAErB,MAAM,oBAAoB;QACxB,IAAI;YACF,eAAe;YAEf,IAAI,YAAY;gBACd,iBAAiB;gBACjB,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa;gBACtD,IAAI,YAAY,MAAM,IAAI;oBACxB,MAAM,eAAe;wBAAE,IAAI,YAAY,EAAE;wBAAE,GAAG,YAAY,IAAI,EAAE;oBAAC;oBACjE,YAAY;oBAEZ,cAAc;oBACd,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,aAAa,OAAO;oBACpE,IAAI,SAAS,MAAM,IAAI;wBACrB,SAAS;4BAAE,IAAI,SAAS,EAAE;4BAAE,GAAG,SAAS,IAAI,EAAE;wBAAC;oBACjD;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA0B;QACnD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,aAAa,OAAO,SAAkC,OAAO;QACjE,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,UAAU;YAEV,MAAM,UAAU;gBACd,QAAQ,KAAK,GAAG;gBAChB,YAAY,cAAc;gBAC1B,MAAM,SAAS,WAAW,IAAI;gBAC9B,YAAY,SAAS,UAAU;gBAC/B;gBACA,MAAM;gBACN,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,iBAAiB;YAC5D,OAAO,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QAEjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oCAChE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC/D,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC7D,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,8OAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,8OAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,8OAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;YAMhC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oCACjF,WAAU;oCACV,aAAY;oCACZ,KAAI;;;;;;8CAEN,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,gBAAgB;oCAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oCACrF,WAAU;oCACV,aAAY;oCACZ,KAAI;;;;;;8CAEN,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;oCACvE,WAAU;;sDAEV,8OAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,8OAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,8OAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,8OAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,8OAAC;4CAAO,OAAO;sDAAI;;;;;;;;;;;;;;;;;;;;;;;;YAM7B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,SAAS,cAAc,CAAC,eAAe,IAAI;oCAClD,UAAU,CAAC,IAAM,kBAAkB,kBAAkB;4CACnD,GAAG,SAAS,cAAc;4CAC1B,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCACjC;oCACA,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,SAAS,cAAc,CAAC,cAAc,IAAI;oCACjD,UAAU,CAAC,IAAM,kBAAkB,kBAAkB;4CACnD,GAAG,SAAS,cAAc;4CAC1B,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAChC;oCACA,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,cAAc,CAAC,QAAQ,IAAI;oCAC3C,UAAU,CAAC,IAAM,kBAAkB,kBAAkB;4CACnD,GAAG,SAAS,cAAc;4CAC1B,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC1B;oCACA,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;;YAMtB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAEvD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAG,WAAU;8DAAyB,SAAS,WAAW;;;;;;;;;;;;sDAE7D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAG,WAAU;8DAAyB,SAAS,UAAU;;;;;;;;;;;;sDAE5D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAG,WAAU;8DAAyB,SAAS,QAAQ,IAAI;;;;;;;;;;;;sDAE9D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAG,WAAU;;wDAAyB,SAAS,SAAS;wDAAC;;;;;;;;;;;;;sDAE5D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAG,WAAU;;wDAAwB;wDAAE,SAAS,YAAY,CAAC,cAAc;;;;;;;;;;;;;sDAE9E,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAG,WAAU;;wDAAwB;wDAAE,SAAS,gBAAgB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;gCAInF,0BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;sDAAyB,SAAS,IAAI;;;;;;;;;;;;;;;;;;sCAK1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,WAAW;oCAC1B,UAAU;oCACV,WAAU;8CAET,SAAS,cAAc;;;;;;8CAE1B,8OAAC;oCACC,SAAS,IAAM,WAAW;oCAC1B,UAAU;oCACV,WAAU;8CAET,SAAS,cAAc;;;;;;;;;;;;;;;;;;YAMlC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAsC;;;;;;;;;;;0CAI1E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;gCAChD,YAAY,uBACX,8OAAC;oCAAE,WAAU;;wCAAqB;sDAChB,8OAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI;;;;;;wCAAQ;wCAAK,MAAM,IAAI;;;;;;;;;;;;;sCAMzF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAW,GAAG,CAAC,GAAG,kBACtC,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC;oDACC,WAAW,CAAC,0EAA0E,EACpF,IAAI,KAAK,cACL,2BACA,6BACJ;8DAED,IAAI;;;;;;gDAEN,IAAI,aAAa,mBAChB,8OAAC;oDACC,WAAW,CAAC,cAAc,EACxB,IAAI,IAAI,cAAc,gBAAgB,eACtC;;;;;;;2CAdE;;;;;;;;;;8CAoBd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;sCACZ;;;;;;wBAIF,cAAc,4BACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU,gBAAgB;oCAC1B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UACE,AAAC,gBAAgB,KAAK,CAAC,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,UAAU,KACpE;oCAEF,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;AAEe,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBACR,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;kBAG3B,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}