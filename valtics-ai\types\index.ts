export interface User {
  id: string;
  email: string;
  role: 'admin' | 'user';
  createdAt: Date;
}

export interface Brand {
  id: string;
  name: string;
  description: string;
  logoUrl: string;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  brandId: string;
  fileUrls: {
    pdf?: string;
    ppt?: string;
    excel?: string;
  };
  price: number;
  createdAt: Date;
}

export interface BVAInstance {
  id: string;
  userId: string;
  templateId: string;
  name: string;
  clientName: string;
  status: 'draft' | 'completed';
  data: Record<string, any>;
  executiveSummaryUrl?: string;
  fullReportUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subscription {
  id: string;
  userId: string;
  status: 'active' | 'inactive';
  type: 'basic' | 'premium';
  startDate: Date;
  endDate: Date;
  accessibleTemplateIds: string[];
}