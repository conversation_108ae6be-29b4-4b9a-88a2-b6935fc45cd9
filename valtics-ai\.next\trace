[{"name": "generate-buildid", "duration": 336, "timestamp": 1074075517570, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748629750676, "traceId": "bfd1980483b4db17"}, {"name": "load-custom-routes", "duration": 277, "timestamp": 1074075518003, "id": 5, "parentId": 1, "tags": {}, "startTime": 1748629750676, "traceId": "bfd1980483b4db17"}, {"name": "create-dist-dir", "duration": 511, "timestamp": 1074075585125, "id": 6, "parentId": 1, "tags": {}, "startTime": 1748629750743, "traceId": "bfd1980483b4db17"}, {"name": "create-pages-mapping", "duration": 246, "timestamp": 1074075598195, "id": 7, "parentId": 1, "tags": {}, "startTime": 1748629750756, "traceId": "bfd1980483b4db17"}, {"name": "collect-app-paths", "duration": 2872, "timestamp": 1074075598501, "id": 8, "parentId": 1, "tags": {}, "startTime": 1748629750757, "traceId": "bfd1980483b4db17"}, {"name": "create-app-mapping", "duration": 3882, "timestamp": 1074075601433, "id": 9, "parentId": 1, "tags": {}, "startTime": 1748629750760, "traceId": "bfd1980483b4db17"}, {"name": "public-dir-conflict-check", "duration": 1301, "timestamp": 1074075605988, "id": 10, "parentId": 1, "tags": {}, "startTime": 1748629750764, "traceId": "bfd1980483b4db17"}, {"name": "generate-routes-manifest", "duration": 4563, "timestamp": 1074075607698, "id": 11, "parentId": 1, "tags": {}, "startTime": 1748629750766, "traceId": "bfd1980483b4db17"}, {"name": "next-build", "duration": 6813546, "timestamp": 1074075402103, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.3", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1748629750560, "traceId": "bfd1980483b4db17"}]