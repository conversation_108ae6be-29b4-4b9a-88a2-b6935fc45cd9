{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/lib/firebase/config.ts"], "sourcesContent": ["import { initializeApp, getApps, getApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,\n};\n\n// Initialize Firebase\nconst app = !getApps().length ? initializeApp(firebaseConfig) : getApp();\nconst auth = getAuth(app);\nconst db = getFirestore(app);\nconst storage = getStorage(app);\n\nexport { app, auth, db, storage };"], "names": [], "mappings": ";;;;;;AAMU;AANV;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,GAAG,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,uLAAA,CAAA,SAAM,AAAD;AACrE,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,sKAAA,CAAA,eAAY,AAAD,EAAE;AACxB,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  User, \n  signInWithEmailAndPassword, \n  createUserWithEmailAndPassword, \n  signOut, \n  onAuthStateChanged,\n  GoogleAuthProvider,\n  signInWithPopup,\n  FacebookAuthProvider\n} from 'firebase/auth';\nimport { auth, db } from '@/lib/firebase/config';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string) => Promise<void>;\n  logOut: () => Promise<void>;\n  signInWithGoogle: () => Promise<void>;\n  signInWithFacebook: () => Promise<void>;\n  isAdmin: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | null>(null);\n\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [isAdmin, setIsAdmin] = useState(false);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      setUser(user);\n      \n      if (user) {\n        // Check if user is admin\n        const userDoc = await getDoc(doc(db, 'users', user.uid));\n        setIsAdmin(userDoc.data()?.role === 'admin');\n      } else {\n        setIsAdmin(false);\n      }\n      \n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    await signInWithEmailAndPassword(auth, email, password);\n  };\n\n  const signUp = async (email: string, password: string) => {\n    const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n    \n    // Create user document in Firestore\n    await setDoc(doc(db, 'users', userCredential.user.uid), {\n      email: userCredential.user.email,\n      role: 'user',\n      createdAt: new Date(),\n    });\n  };\n\n  const logOut = async () => {\n    await signOut(auth);\n  };\n\n  const signInWithGoogle = async () => {\n    const provider = new GoogleAuthProvider();\n    const userCredential = await signInWithPopup(auth, provider);\n    \n    // Check if user exists in Firestore, if not create a new document\n    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));\n    \n    if (!userDoc.exists()) {\n      await setDoc(doc(db, 'users', userCredential.user.uid), {\n        email: userCredential.user.email,\n        role: 'user',\n        createdAt: new Date(),\n      });\n    }\n  };\n\n  const signInWithFacebook = async () => {\n    const provider = new FacebookAuthProvider();\n    const userCredential = await signInWithPopup(auth, provider);\n    \n    // Check if user exists in Firestore, if not create a new document\n    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));\n    \n    if (!userDoc.exists()) {\n      await setDoc(doc(db, 'users', userCredential.user.uid), {\n        email: userCredential.user.email,\n        role: 'user',\n        createdAt: new Date(),\n      });\n    }\n  };\n\n  return (\n    <AuthContext.Provider value={{ \n      user, \n      loading, \n      signIn, \n      signUp, \n      logOut, \n      signInWithGoogle, \n      signInWithFacebook,\n      isAdmin \n    }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAAA;;;AAdA;;;;;AA2BA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA0B;AAEnD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,4HAAA,CAAA,OAAI;sDAAE,OAAO;oBAClD,QAAQ;oBAER,IAAI,MAAM;wBACR,yBAAyB;wBACzB,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG;wBACtD,WAAW,QAAQ,IAAI,IAAI,SAAS;oBACtC,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAEA;0CAAO,IAAM;;QACf;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE,OAAO;IAChD;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE,OAAO;QAEzE,oCAAoC;QACpC,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;YACtD,OAAO,eAAe,IAAI,CAAC,KAAK;YAChC,MAAM;YACN,WAAW,IAAI;QACjB;IACF;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE,4HAAA,CAAA,OAAI;IACpB;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,IAAI,wNAAA,CAAA,qBAAkB;QACvC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE;QAEnD,kEAAkE;QAClE,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG;QAErE,IAAI,CAAC,QAAQ,MAAM,IAAI;YACrB,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;gBACtD,OAAO,eAAe,IAAI,CAAC,KAAK;gBAChC,MAAM;gBACN,WAAW,IAAI;YACjB;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,WAAW,IAAI,0NAAA,CAAA,uBAAoB;QACzC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE;QAEnD,kEAAkE;QAClE,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG;QAErE,IAAI,CAAC,QAAQ,MAAM,IAAI;YACrB,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;gBACtD,OAAO,eAAe,IAAI,CAAC,KAAK;gBAChC,MAAM;gBACN,WAAW,IAAI;YACjB;QACF;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GAxFa;KAAA;AA0FN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | null>(null);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\n// Safe version that returns null instead of throwing\nexport const useThemeSafe = () => {\n  const context = useContext(ThemeContext);\n  return context;\n};\n\nexport const ThemeProvider = ({ children }: { children: React.ReactNode }) => {\n  const [theme, setTheme] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Handle mounting to prevent hydration mismatch\n  useEffect(() => {\n    setMounted(true);\n\n    // Get theme from localStorage or system preference\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme) {\n      setTheme(savedTheme);\n      applyTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const initialTheme = systemPrefersDark ? 'dark' : 'light';\n      setTheme(initialTheme);\n      applyTheme(initialTheme);\n    }\n  }, []);\n\n  // Apply theme to document\n  const applyTheme = (newTheme: Theme) => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    applyTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return <>{children}</>;\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAWA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA2B;AAErD,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AASN,MAAM,eAAe;;IAC1B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,OAAO;AACT;IAHa;AAKN,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;YAEX,mDAAmD;YACnD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;gBACT,WAAW;YACb,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;gBACT,WAAW;YACb;QACF;kCAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C,SAAS;QACT,WAAW;QACX,aAAa,OAAO,CAAC,SAAS;IAChC;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP;IAjDa;KAAA", "debugId": null}}]}