{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/lib/firebase/config.ts"], "sourcesContent": ["import { initializeApp, getApps, getApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,\n};\n\n// Initialize Firebase\nconst app = !getApps().length ? initializeApp(firebaseConfig) : getApp();\nconst auth = getAuth(app);\nconst db = getFirestore(app);\nconst storage = getStorage(app);\n\nexport { app, auth, db, storage };"], "names": [], "mappings": ";;;;;;AAMU;AANV;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,GAAG,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,uLAAA,CAAA,SAAM,AAAD;AACrE,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,sKAAA,CAAA,eAAY,AAAD,EAAE;AACxB,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  User, \n  signInWithEmailAndPassword, \n  createUserWithEmailAndPassword, \n  signOut, \n  onAuthStateChanged,\n  GoogleAuthProvider,\n  signInWithPopup,\n  FacebookAuthProvider\n} from 'firebase/auth';\nimport { auth, db } from '@/lib/firebase/config';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string) => Promise<void>;\n  logOut: () => Promise<void>;\n  signInWithGoogle: () => Promise<void>;\n  signInWithFacebook: () => Promise<void>;\n  isAdmin: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | null>(null);\n\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [isAdmin, setIsAdmin] = useState(false);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      setUser(user);\n      \n      if (user) {\n        // Check if user is admin\n        const userDoc = await getDoc(doc(db, 'users', user.uid));\n        setIsAdmin(userDoc.data()?.role === 'admin');\n      } else {\n        setIsAdmin(false);\n      }\n      \n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    await signInWithEmailAndPassword(auth, email, password);\n  };\n\n  const signUp = async (email: string, password: string) => {\n    const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n    \n    // Create user document in Firestore\n    await setDoc(doc(db, 'users', userCredential.user.uid), {\n      email: userCredential.user.email,\n      role: 'user',\n      createdAt: new Date(),\n    });\n  };\n\n  const logOut = async () => {\n    await signOut(auth);\n  };\n\n  const signInWithGoogle = async () => {\n    const provider = new GoogleAuthProvider();\n    const userCredential = await signInWithPopup(auth, provider);\n    \n    // Check if user exists in Firestore, if not create a new document\n    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));\n    \n    if (!userDoc.exists()) {\n      await setDoc(doc(db, 'users', userCredential.user.uid), {\n        email: userCredential.user.email,\n        role: 'user',\n        createdAt: new Date(),\n      });\n    }\n  };\n\n  const signInWithFacebook = async () => {\n    const provider = new FacebookAuthProvider();\n    const userCredential = await signInWithPopup(auth, provider);\n    \n    // Check if user exists in Firestore, if not create a new document\n    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));\n    \n    if (!userDoc.exists()) {\n      await setDoc(doc(db, 'users', userCredential.user.uid), {\n        email: userCredential.user.email,\n        role: 'user',\n        createdAt: new Date(),\n      });\n    }\n  };\n\n  return (\n    <AuthContext.Provider value={{ \n      user, \n      loading, \n      signIn, \n      signUp, \n      logOut, \n      signInWithGoogle, \n      signInWithFacebook,\n      isAdmin \n    }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAAA;;;AAdA;;;;;AA2BA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA0B;AAEnD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,4HAAA,CAAA,OAAI;sDAAE,OAAO;oBAClD,QAAQ;oBAER,IAAI,MAAM;wBACR,yBAAyB;wBACzB,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG;wBACtD,WAAW,QAAQ,IAAI,IAAI,SAAS;oBACtC,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAEA;0CAAO,IAAM;;QACf;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE,OAAO;IAChD;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE,OAAO;QAEzE,oCAAoC;QACpC,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;YACtD,OAAO,eAAe,IAAI,CAAC,KAAK;YAChC,MAAM;YACN,WAAW,IAAI;QACjB;IACF;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE,4HAAA,CAAA,OAAI;IACpB;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,IAAI,wNAAA,CAAA,qBAAkB;QACvC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE;QAEnD,kEAAkE;QAClE,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG;QAErE,IAAI,CAAC,QAAQ,MAAM,IAAI;YACrB,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;gBACtD,OAAO,eAAe,IAAI,CAAC,KAAK;gBAChC,MAAM;gBACN,WAAW,IAAI;YACjB;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,WAAW,IAAI,0NAAA,CAAA,uBAAoB;QACzC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,4HAAA,CAAA,OAAI,EAAE;QAEnD,kEAAkE;QAClE,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG;QAErE,IAAI,CAAC,QAAQ,MAAM,IAAI;YACrB,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;gBACtD,OAAO,eAAe,IAAI,CAAC,KAAK;gBAChC,MAAM;gBACN,WAAW,IAAI;YACjB;QACF;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GAxFa;KAAA;AA0FN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | null>(null);\n\nexport const ThemeProvider = ({ children }: { children: React.ReactNode }) => {\n  const [theme, setThemeState] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Load theme from localStorage on mount\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('valtics-theme') as Theme;\n\n    let initialTheme: Theme;\n    if (savedTheme) {\n      initialTheme = savedTheme;\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      initialTheme = systemPrefersDark ? 'dark' : 'light';\n    }\n\n    // Apply theme immediately\n    const root = document.documentElement;\n    if (initialTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n\n    setThemeState(initialTheme);\n    setMounted(true);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (mounted) {\n      const root = document.documentElement;\n\n      if (theme === 'dark') {\n        root.classList.add('dark');\n      } else {\n        root.classList.remove('dark');\n      }\n\n      localStorage.setItem('valtics-theme', theme);\n    }\n  }, [theme, mounted]);\n\n  const toggleTheme = () => {\n    setThemeState(prev => prev === 'light' ? 'dark' : 'light');\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n  };\n\n  // Prevent hydration mismatch\n  if (!mounted) {\n    return <div className=\"min-h-screen bg-white\">{children}</div>;\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAYA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA2B;AAErD,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;;IACvE,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI;YACJ,IAAI,YAAY;gBACd,eAAe;YACjB,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,eAAe,oBAAoB,SAAS;YAC9C;YAEA,0BAA0B;YAC1B,MAAM,OAAO,SAAS,eAAe;YACrC,IAAI,iBAAiB,QAAQ;gBAC3B,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;YAEA,cAAc;YACd,WAAW;QACb;kCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS;gBACX,MAAM,OAAO,SAAS,eAAe;gBAErC,IAAI,UAAU,QAAQ;oBACpB,KAAK,SAAS,CAAC,GAAG,CAAC;gBACrB,OAAO;oBACL,KAAK,SAAS,CAAC,MAAM,CAAC;gBACxB;gBAEA,aAAa,OAAO,CAAC,iBAAiB;YACxC;QACF;kCAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,cAAc,CAAA,OAAQ,SAAS,UAAU,SAAS;IACpD;IAEA,MAAM,WAAW,CAAC;QAChB,cAAc;IAChB;IAEA,6BAA6B;IAC7B,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAI,WAAU;sBAAyB;;;;;;IACjD;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAa;QAAS;kBAC1D;;;;;;AAGP;GA9Da;KAAA;AAgEN,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}]}