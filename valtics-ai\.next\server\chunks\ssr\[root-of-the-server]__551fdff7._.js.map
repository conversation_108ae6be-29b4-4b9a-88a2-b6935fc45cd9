{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,sCAAsC;QACtC,IAAI,CAAC,cAAc;YACjB,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;gBACT,WAAW;YACb,OAAO;gBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;gBACT,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/pricing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport ThemeToggle from '@/components/ThemeToggle';\n\nexport default function Pricing() {\n  const { user } = useAuth();\n\n  const plans = [\n    {\n      name: 'Basic',\n      price: 29,\n      period: 'month',\n      description: 'Perfect for small teams getting started with BVA',\n      features: [\n        'Up to 5 BVAs per month',\n        'Access to basic templates',\n        'Standard support',\n        'PDF exports',\n        'Basic analytics'\n      ],\n      buttonText: 'Start Basic Plan',\n      popular: false\n    },\n    {\n      name: 'Premium',\n      price: 79,\n      period: 'month',\n      description: 'Ideal for growing businesses with advanced needs',\n      features: [\n        'Unlimited BVAs',\n        'Access to all templates',\n        'Priority support',\n        'PDF & PowerPoint exports',\n        'Advanced analytics',\n        'Custom branding',\n        'API access'\n      ],\n      buttonText: 'Start Premium Plan',\n      popular: true\n    },\n    {\n      name: 'Enterprise',\n      price: 199,\n      period: 'month',\n      description: 'For large organizations with complex requirements',\n      features: [\n        'Everything in Premium',\n        'Custom templates',\n        'Dedicated account manager',\n        'SSO integration',\n        'Advanced security',\n        'Custom integrations',\n        'Training & onboarding'\n      ],\n      buttonText: 'Contact Sales',\n      popular: false\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Navigation */}\n      <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href={user ? \"/dashboard\" : \"/\"} className=\"flex items-center space-x-3\">\n                <Image\n                  src=\"/logo.png\"\n                  alt=\"VALTICS AI Logo\"\n                  width={32}\n                  height={32}\n                  className=\"w-8 h-8\"\n                />\n                <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                  VALTICS AI\n                </span>\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <Link\n                  href=\"/dashboard\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Dashboard\n                </Link>\n              ) : (\n                <>\n                  <Link\n                    href=\"/login\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Login\n                  </Link>\n                  <ThemeToggle />\n                  <Link\n                    href=\"/register\"\n                    className=\"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n                  >\n                    Sign Up\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-12 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Choose Your Plan\n            </h1>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n              Select the perfect plan for your business value analysis needs.\n              All plans include access to our AI-powered BVA platform.\n            </p>\n          </div>\n\n          {/* Pricing Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n            {plans.map((plan, index) => (\n              <div\n                key={plan.name}\n                className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden ${\n                  plan.popular ? 'ring-2 ring-blue-500 dark:ring-blue-400' : ''\n                }`}\n              >\n                {plan.popular && (\n                  <div className=\"absolute top-0 left-0 right-0 bg-blue-500 dark:bg-blue-600 text-white text-center py-2 text-sm font-medium\">\n                    Most Popular\n                  </div>\n                )}\n\n                <div className={`p-8 ${plan.popular ? 'pt-12' : ''}`}>\n                  <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">{plan.name}</h3>\n                  <p className=\"text-gray-600 dark:text-gray-300 mb-6\">{plan.description}</p>\n\n                  <div className=\"mb-6\">\n                    <span className=\"text-4xl font-bold text-gray-900 dark:text-white\">${plan.price}</span>\n                    <span className=\"text-gray-600 dark:text-gray-400\">/{plan.period}</span>\n                  </div>\n\n                  <ul className=\"space-y-3 mb-8\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-start\">\n                        <div className=\"flex-shrink-0 w-5 h-5 bg-green-500 dark:bg-green-600 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                          <span className=\"text-white text-xs\">✓</span>\n                        </div>\n                        <span className=\"text-gray-700 dark:text-gray-300\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  <button\n                    className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${\n                      plan.popular\n                        ? 'bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-800'\n                        : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600'\n                    }`}\n                  >\n                    {plan.buttonText}\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* FAQ Section */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center\">\n              Frequently Asked Questions\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Can I change my plan later?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Is there a free trial?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  We offer a 14-day free trial for all new users. No credit card required to get started.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  What payment methods do you accept?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  We accept all major credit cards, PayPal, and bank transfers for enterprise customers.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Can I cancel anytime?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Do you offer custom templates?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Custom templates are available for Enterprise customers. Contact our sales team for more information.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Is my data secure?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Yes, we use enterprise-grade security measures including encryption, secure data centers, and regular security audits.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* CTA Section */}\n          <div className=\"text-center mt-16\">\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n              Ready to Get Started?\n            </h2>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8\">\n              Join thousands of businesses already using VALTICS AI to create compelling business value analyses.\n            </p>\n            <div className=\"flex justify-center space-x-4\">\n              {!user && (\n                <Link\n                  href=\"/register\"\n                  className=\"bg-blue-600 dark:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n                >\n                  Start Free Trial\n                </Link>\n              )}\n              <button className=\"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600\">\n                Contact Sales\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,OAAO,eAAe;oCAAK,WAAU;;sDAC/C,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAsD;;;;;;;;;;;;;;;;;0CAK1E,8OAAC;gCAAI,WAAU;0CACZ,qBACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;yDAID;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,0HAAA,CAAA,UAAW;;;;;sDACZ,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,8OAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;sCAO5E,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAEC,WAAW,CAAC,wEAAwE,EAClF,KAAK,OAAO,GAAG,4CAA4C,IAC3D;;wCAED,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;sDAA6G;;;;;;sDAK9H,8OAAC;4CAAI,WAAW,CAAC,IAAI,EAAE,KAAK,OAAO,GAAG,UAAU,IAAI;;8DAClD,8OAAC;oDAAG,WAAU;8DAAyD,KAAK,IAAI;;;;;;8DAChF,8OAAC;oDAAE,WAAU;8DAAyC,KAAK,WAAW;;;;;;8DAEtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAmD;gEAAE,KAAK,KAAK;;;;;;;sEAC/E,8OAAC;4DAAK,WAAU;;gEAAmC;gEAAE,KAAK,MAAM;;;;;;;;;;;;;8DAGlE,8OAAC;oDAAG,WAAU;8DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;4DAAsB,WAAU;;8EAC/B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;8EAEvC,8OAAC;oEAAK,WAAU;8EAAoC;;;;;;;2DAJ7C;;;;;;;;;;8DASb,8OAAC;oDACC,WAAW,CAAC,0DAA0D,EACpE,KAAK,OAAO,GACR,qFACA,uGACJ;8DAED,KAAK,UAAU;;;;;;;;;;;;;mCAtCf,KAAK,IAAI;;;;;;;;;;sCA8CpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAIlF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;sCAQtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,sBACA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAIH,8OAAC;4CAAO,WAAU;sDAA+I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/K", "debugId": null}}]}