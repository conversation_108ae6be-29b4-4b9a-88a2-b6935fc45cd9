(()=>{var e={};e.id=213,e.ids=[213],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,s)=>{Promise.resolve().then(s.bind(s,94442)),Promise.resolve().then(s.bind(s,3465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,t,s)=>{"use strict";s.d(t,{Q:()=>n,ThemeProvider:()=>o});var r=s(60687),a=s(43210);let i=(0,a.createContext)(null),n=()=>(0,a.useContext)(i),o=({children:e})=>{let[t,s]=(0,a.useState)("light"),[n,o]=(0,a.useState)(!1);(0,a.useEffect)(()=>{o(!0);let e=localStorage.getItem("theme");if(e)s(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return n?(0,r.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";s(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,r.jsx)(r.Fragment,{children:e})}},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,t,s)=>{"use strict";s.d(t,{A:()=>c,AuthProvider:()=>d});var r=s(60687),a=s(43210),i=s(19978),n=s(56304),o=s(75535);let l=(0,a.createContext)(null),d=({children:e})=>{let[t,s]=(0,a.useState)(null),[d,c]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=(0,i.hg)(n.j2,async e=>{if(s(e),e){let t=await (0,o.x7)((0,o.H9)(n.db,"users",e.uid));m(t.data()?.role==="admin")}else m(!1);c(!1)});return()=>e()},[]);let h=async(e,t)=>{await (0,i.x9)(n.j2,e,t)},p=async(e,t)=>{let s=await (0,i.eJ)(n.j2,e,t);await (0,o.BN)((0,o.H9)(n.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})},x=async()=>{await (0,i.CI)(n.j2)},v=async()=>{let e=new i.HF,t=await (0,i.df)(n.j2,e);(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()||await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},b=async()=>{let e=new i.sk,t=await (0,i.df)(n.j2,e);(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()||await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,r.jsx)(l.Provider,{value:{user:t,loading:d,signIn:h,signUp:p,logOut:x,signInWithGoogle:v,signInWithFacebook:b,isAdmin:u},children:e})},c=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,s)=>{"use strict";s.d(t,{db:()=>d,j2:()=>l});var r=s(67989),a=s(19978),i=s(75535),n=s(70146);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,a.xI)(o),d=(0,i.aU)(o);(0,n.c7)(o)},57160:(e,t,s)=>{Promise.resolve().then(s.bind(s,86636))},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(37413),a=s(61421),i=s.n(a);s(82704);var n=s(94442),o=s(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(o.ThemeProvider,{children:(0,r.jsx)(n.AuthProvider,{children:e})})})})}},58497:(e,t,s)=>{Promise.resolve().then(s.bind(s,51108)),Promise.resolve().then(s.bind(s,40303))},61589:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\brands\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\brands\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},86636:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(60687),a=s(43210),i=s(51108),n=s(16189),o=s(85814),l=s.n(o);function d(){let{user:e,loading:t}=(0,i.A)();(0,n.useRouter)();let[s,o]=(0,a.useState)([]),[d,c]=(0,a.useState)({}),[u,m]=(0,a.useState)(!0),[h,p]=(0,a.useState)(null);return t||u?(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})}):e?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(l(),{href:"/dashboard",className:"text-xl font-semibold text-gray-900",children:"VALTICS AI"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(l(),{href:"/dashboard",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Dashboard"}),(0,r.jsx)(l(),{href:"/templates",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Templates"})]})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Solution Brands"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600",children:"Explore our comprehensive collection of technology solution brands and their BVA templates."})]}),s.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[e.logoUrl&&(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("img",{src:e.logoUrl,alt:`${e.name} logo`,className:"h-12 w-auto"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:e.name}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[d[e.id]?.length||0," templates available"]}),(0,r.jsx)("button",{onClick:()=>p(h===e.id?null:e.id),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:h===e.id?"Hide Templates":"View Templates"})]}),h===e.id&&d[e.id]&&(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Available Templates:"}),(0,r.jsx)("div",{className:"space-y-2",children:d[e.id].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.category})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-green-600",children:["$",e.price]}),(0,r.jsx)(l(),{href:`/templates/${e.id}`,className:"bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-blue-700",children:"Use"})]})]},e.id))})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(l(),{href:`/templates?brand=${e.id}`,className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 inline-block text-center",children:["Explore ",e.name," Templates"]})})]})},e.id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No brands available"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Check back later for new solution brands and templates."})]})]})})]}):null}s(75535),s(56304)},91645:e=>{"use strict";e.exports=require("net")},93608:(e,t,s)=>{Promise.resolve().then(s.bind(s,80282))},94442:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94734:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["brands",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80282)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\brands\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\brands\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/brands/page",pathname:"/brands",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,823,567],()=>s(94734));module.exports=r})();