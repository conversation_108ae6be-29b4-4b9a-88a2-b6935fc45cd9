(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{5489:(e,s,a)=>{Promise.resolve().then(a.bind(a,8007))},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})},8007:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var t=a(5155),r=a(2115),i=a(9481),l=a(5695),d=a(6874),u=a.n(d);function n(){let[e,s]=(0,r.useState)(""),[a,d]=(0,r.useState)(""),[n,o]=(0,r.useState)(""),{signIn:c,signInWithGoogle:m,signInWithFacebook:x}=(0,i.A)(),h=(0,l.useRouter)(),b=async s=>{s.preventDefault(),o("");try{await c(e,a),h.push("/dashboard")}catch(e){o(e.message)}},f=async()=>{try{await m(),h.push("/dashboard")}catch(e){o(e.message)}},p=async()=>{try{await x(),h.push("/dashboard")}catch(e){o(e.message)}};return(0,t.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,t.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Login to VALTICS AI"})}),n&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:n}),(0,t.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:a,onChange:e=>d(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Sign in"})})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,t.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,t.jsx)("button",{onClick:f,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Google"}),(0,t.jsx)("button",{onClick:p,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Facebook"})]})]}),(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(u(),{href:"/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"Register"})]})})]})})}},9481:(e,s,a)=>{"use strict";a.d(s,{AuthProvider:()=>x,A:()=>h});var t=a(5155),r=a(2115),i=a(6203),l=a(3915),d=a(5317),u=a(858);let n=(0,l.Dk)().length?(0,l.Sx)():(0,l.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),o=(0,i.xI)(n),c=(0,d.aU)(n);(0,u.c7)(n);let m=(0,r.createContext)(null),x=e=>{let{children:s}=e,[a,l]=(0,r.useState)(null),[u,n]=(0,r.useState)(!0),[x,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=(0,i.hg)(o,async e=>{if(l(e),e){var s;h((null==(s=(await (0,d.x7)((0,d.H9)(c,"users",e.uid))).data())?void 0:s.role)==="admin")}else h(!1);n(!1)});return()=>e()},[]);let b=async(e,s)=>{await (0,i.x9)(o,e,s)},f=async(e,s)=>{let a=await (0,i.eJ)(o,e,s);await (0,d.BN)((0,d.H9)(c,"users",a.user.uid),{email:a.user.email,role:"user",createdAt:new Date})},p=async()=>{await (0,i.CI)(o)},g=async()=>{let e=new i.HF,s=await (0,i.df)(o,e);(await (0,d.x7)((0,d.H9)(c,"users",s.user.uid))).exists()||await (0,d.BN)((0,d.H9)(c,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})},w=async()=>{let e=new i.sk,s=await (0,i.df)(o,e);(await (0,d.x7)((0,d.H9)(c,"users",s.user.uid))).exists()||await (0,d.BN)((0,d.H9)(c,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})};return(0,t.jsx)(m.Provider,{value:{user:a,loading:u,signIn:b,signUp:f,logOut:p,signInWithGoogle:g,signInWithFacebook:w,isAdmin:x},children:s})},h=()=>{let e=(0,r.useContext)(m);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,288,874,441,684,358],()=>s(5489)),_N_E=e.O()}]);