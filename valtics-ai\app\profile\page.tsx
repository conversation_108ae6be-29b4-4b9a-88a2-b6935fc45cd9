'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { doc, getDoc, updateDoc, collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { User, BVAInstance, Subscription } from '@/types';

export default function Profile() {
  const { user, loading, logOut } = useAuth();
  const { theme, setTheme } = useTheme();
  const router = useRouter();

  const [userProfile, setUserProfile] = useState<User | null>(null);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [bvaInstances, setBvaInstances] = useState<BVAInstance[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [activeTab, setActiveTab] = useState('profile');
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user) {
      fetchProfileData();
    }
  }, [user]);

  const fetchProfileData = async () => {
    try {
      setLoadingData(true);

      // Fetch user profile
      const userDoc = await getDoc(doc(db, 'users', user!.uid));
      if (userDoc.exists()) {
        setUserProfile({ id: userDoc.id, ...userDoc.data() } as User);
      }

      // Fetch subscription
      const subscriptionQuery = query(
        collection(db, 'subscriptions'),
        where('userId', '==', user!.uid)
      );
      const subscriptionSnapshot = await getDocs(subscriptionQuery);
      if (!subscriptionSnapshot.empty) {
        const subscriptionData = subscriptionSnapshot.docs[0];
        setSubscription({ id: subscriptionData.id, ...subscriptionData.data() } as Subscription);
      }

      // Fetch user's BVA instances
      const bvaQuery = query(
        collection(db, 'bvaInstances'),
        where('userId', '==', user!.uid),
        orderBy('updatedAt', 'desc')
      );
      const bvaSnapshot = await getDocs(bvaQuery);
      const bvaData = bvaSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as BVAInstance[];
      setBvaInstances(bvaData);

    } catch (error) {
      console.error('Error fetching profile data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logOut();
      router.push('/');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    if (!user || !userProfile) return;

    try {
      setUpdating(true);
      await updateDoc(doc(db, 'users', user.uid), updates);
      setUserProfile({ ...userProfile, ...updates });
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Error updating profile. Please try again.');
    } finally {
      setUpdating(false);
    }
  };

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!user || !userProfile) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderProfile = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Account Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email Address
              </label>
              <input
                type="email"
                value={user.email || ''}
                disabled
                className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Email cannot be changed</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Account Type
              </label>
              <input
                type="text"
                value={userProfile.role === 'admin' ? 'Administrator' : 'Standard User'}
                disabled
                className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Member Since
              </label>
              <input
                type="text"
                value={new Date(userProfile.createdAt).toLocaleDateString()}
                disabled
                className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Total BVAs Created
              </label>
              <input
                type="text"
                value={bvaInstances.length.toString()}
                disabled
                className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Theme Settings */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Appearance Settings</h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-slate-600 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Theme Preference</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">Choose your preferred color scheme</p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setTheme('light')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    theme === 'light'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600'
                  }`}
                >
                  Light
                </button>
                <button
                  onClick={() => setTheme('dark')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    theme === 'dark'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600'
                  }`}
                >
                  Dark
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Account Actions</h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-slate-600 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Change Password</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">Update your account password</p>
              </div>
              <button className="bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800">
                Change Password
              </button>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-slate-600 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Download Data</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">Export all your BVA data</p>
              </div>
              <button className="bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-800">
                Export Data
              </button>
            </div>

            <div className="flex items-center justify-between p-4 border border-red-200 dark:border-red-600 rounded-lg">
              <div>
                <h4 className="font-medium text-red-900 dark:text-red-300">Sign Out</h4>
                <p className="text-sm text-red-600 dark:text-red-400">Sign out of your account</p>
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSubscription = () => (
    <div className="space-y-6">
      {subscription ? (
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Current Subscription</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Plan Type
                </label>
                <div className="flex items-center">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    subscription.type === 'enterprise' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300' :
                    subscription.type === 'premium' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300' :
                    'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                  }`}>
                    {subscription.type.charAt(0).toUpperCase() + subscription.type.slice(1)}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  subscription.status === 'active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300' :
                  subscription.status === 'cancelled' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300' :
                  'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                }`}>
                  {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Start Date
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {new Date(subscription.startDate).toLocaleDateString()}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  End Date
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {new Date(subscription.endDate).toLocaleDateString()}
                </p>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Template Access
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {subscription.accessibleTemplateIds.length} templates available
                </p>
              </div>
            </div>

            <div className="mt-6 flex space-x-4">
              <button className="bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800">
                Upgrade Plan
              </button>
              <button className="bg-gray-300 dark:bg-slate-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400 dark:hover:bg-slate-500">
                Manage Billing
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
          <div className="p-6 text-center">
            <div className="text-gray-400 dark:text-gray-500 text-6xl mb-4">💳</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Active Subscription</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Subscribe to access premium templates and features.
            </p>
            <button className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800">
              View Subscription Plans
            </button>
          </div>
        </div>
      )}
    </div>
  );

  const renderBVAs = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Your BVAs</h3>

          {bvaInstances.length > 0 ? (
            <div className="space-y-4">
              {bvaInstances.map((bva) => (
                <div key={bva.id} className="border border-gray-200 dark:border-slate-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{bva.name}</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Client: {bva.clientName}</p>
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        Last updated: {new Date(bva.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(bva.status)}`}>
                        {bva.status}
                      </span>
                      <Link
                        href={`/bva/${bva.id}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
                      >
                        View
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 dark:text-gray-500 text-4xl mb-4">📊</div>
              <p className="text-gray-500 dark:text-gray-400">No BVAs created yet.</p>
              <Link
                href="/bva/new"
                className="mt-4 inline-block bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800"
              >
                Create Your First BVA
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-slate-800 shadow-sm border-b border-gray-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-xl font-semibold text-gray-900 dark:text-white">
                VALTICS AI
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Profile & Settings</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-300">
              Manage your account settings and view your activity.
            </p>
          </div>

          {/* Tabs */}
          <div className="mb-8">
            <nav className="flex space-x-8">
              {[
                { id: 'profile', name: 'Profile' },
                { id: 'subscription', name: 'Subscription' },
                { id: 'bvas', name: 'My BVAs' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-slate-600'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'profile' && renderProfile()}
          {activeTab === 'subscription' && renderSubscription()}
          {activeTab === 'bvas' && renderBVAs()}
        </div>
      </div>
    </div>
  );
}
