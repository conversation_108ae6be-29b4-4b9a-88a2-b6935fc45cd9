'use client';

import { useEffect, useState, Suspense } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { doc, getDoc, collection, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Template, Brand, BVAFormData } from '@/types';
import Navigation from '@/components/Navigation';
import TrialRestriction from '@/components/TrialRestriction';

function NewBVAContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const templateId = searchParams.get('template');

  const [template, setTemplate] = useState<Template | null>(null);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [loadingData, setLoadingData] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<BVAFormData>({
    clientName: '',
    projectName: '',
    industry: '',
    currentCosts: 0,
    expectedBenefits: 0,
    timeframe: 12,
    additionalData: {}
  });
  const [saving, setSaving] = useState(false);

  const totalSteps = 4;

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user && templateId) {
      fetchTemplateData();
    } else if (user && !templateId) {
      setLoadingData(false);
    }
  }, [user, templateId]);

  const fetchTemplateData = async () => {
    try {
      setLoadingData(true);

      if (templateId) {
        // Fetch template
        const templateDoc = await getDoc(doc(db, 'templates', templateId));
        if (templateDoc.exists()) {
          const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;
          setTemplate(templateData);

          // Fetch brand
          const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));
          if (brandDoc.exists()) {
            setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);
          }
        }
      }

    } catch (error) {
      console.error('Error fetching template data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const handleInputChange = (field: keyof BVAFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSave = async (status: 'draft' | 'in-progress' = 'draft') => {
    if (!user) return;

    try {
      setSaving(true);

      const bvaData = {
        userId: user.uid,
        templateId: templateId || '',
        name: formData.projectName || 'Untitled BVA',
        clientName: formData.clientName,
        status,
        data: formData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const docRef = await addDoc(collection(db, 'bvaInstances'), bvaData);
      router.push(`/bva/${docRef.id}`);

    } catch (error) {
      console.error('Error saving BVA:', error);
      alert('Error saving BVA. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-xl text-gray-900 dark:text-white">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Project Information</h2>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Project Name *
              </label>
              <input
                type="text"
                value={formData.projectName}
                onChange={(e) => handleInputChange('projectName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter project name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Client Name *
              </label>
              <input
                type="text"
                value={formData.clientName}
                onChange={(e) => handleInputChange('clientName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter client name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Industry
              </label>
              <select
                value={formData.industry}
                onChange={(e) => handleInputChange('industry', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select industry</option>
                <option value="Technology">Technology</option>
                <option value="Healthcare">Healthcare</option>
                <option value="Finance">Finance</option>
                <option value="Manufacturing">Manufacturing</option>
                <option value="Retail">Retail</option>
                <option value="Education">Education</option>
                <option value="Government">Government</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Financial Information</h2>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Annual Costs ($)
              </label>
              <input
                type="number"
                value={formData.currentCosts}
                onChange={(e) => handleInputChange('currentCosts', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
                min="0"
              />
              <p className="text-sm text-gray-500 mt-1">
                Current annual costs related to this solution area
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expected Annual Benefits ($)
              </label>
              <input
                type="number"
                value={formData.expectedBenefits}
                onChange={(e) => handleInputChange('expectedBenefits', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
                min="0"
              />
              <p className="text-sm text-gray-500 mt-1">
                Expected annual benefits from the new solution
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Analysis Timeframe (months)
              </label>
              <select
                value={formData.timeframe}
                onChange={(e) => handleInputChange('timeframe', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={12}>12 months</option>
                <option value={24}>24 months</option>
                <option value={36}>36 months</option>
                <option value={48}>48 months</option>
                <option value={60}>60 months</option>
              </select>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Additional Details</h2>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Key Business Drivers
              </label>
              <textarea
                value={formData.additionalData.businessDrivers || ''}
                onChange={(e) => handleInputChange('additionalData', {
                  ...formData.additionalData,
                  businessDrivers: e.target.value
                })}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe the key business drivers for this project..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Success Metrics
              </label>
              <textarea
                value={formData.additionalData.successMetrics || ''}
                onChange={(e) => handleInputChange('additionalData', {
                  ...formData.additionalData,
                  successMetrics: e.target.value
                })}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Define how success will be measured..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Implementation Timeline
              </label>
              <input
                type="text"
                value={formData.additionalData.timeline || ''}
                onChange={(e) => handleInputChange('additionalData', {
                  ...formData.additionalData,
                  timeline: e.target.value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., 6 months implementation, 3 months rollout"
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Review & Save</h2>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">BVA Summary</h3>

              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Project Name</dt>
                  <dd className="text-sm text-gray-900">{formData.projectName}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Client</dt>
                  <dd className="text-sm text-gray-900">{formData.clientName}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Industry</dt>
                  <dd className="text-sm text-gray-900">{formData.industry || 'Not specified'}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Timeframe</dt>
                  <dd className="text-sm text-gray-900">{formData.timeframe} months</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Current Costs</dt>
                  <dd className="text-sm text-gray-900">${formData.currentCosts.toLocaleString()}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Expected Benefits</dt>
                  <dd className="text-sm text-gray-900">${formData.expectedBenefits.toLocaleString()}</dd>
                </div>
              </dl>

              {template && (
                <div className="mt-4 pt-4 border-t">
                  <dt className="text-sm font-medium text-gray-500">Template</dt>
                  <dd className="text-sm text-gray-900">{template.name}</dd>
                </div>
              )}
            </div>

            <div className="flex space-x-4">
              <button
                onClick={() => handleSave('draft')}
                disabled={saving}
                className="flex-1 bg-gray-600 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-700 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save as Draft'}
              </button>
              <button
                onClick={() => handleSave('in-progress')}
                disabled={saving}
                className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save & Continue'}
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation title="VALTICS AI" showBackButton={true} />

      {/* Main Content */}
      <div className="max-w-3xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Create New BVA</h1>
            {template && brand && (
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                Using template: <span className="font-medium">{template.name}</span> by {brand.name}
              </p>
            )}
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center">
              {Array.from({ length: totalSteps }, (_, i) => (
                <div key={i} className="flex items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      i + 1 <= currentStep
                        ? 'bg-blue-600 dark:bg-blue-700 text-white'
                        : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                    }`}
                  >
                    {i + 1}
                  </div>
                  {i < totalSteps - 1 && (
                    <div
                      className={`w-16 h-1 mx-2 ${
                        i + 1 < currentStep ? 'bg-blue-600 dark:bg-blue-700' : 'bg-gray-200 dark:bg-gray-600'
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-600 dark:text-gray-400">
              <span>Project Info</span>
              <span>Financial</span>
              <span>Details</span>
              <span>Review</span>
            </div>
          </div>

          {/* Form Content */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8">
            {renderStep()}
          </div>

          {/* Navigation Buttons */}
          {currentStep < totalSteps && (
            <div className="flex justify-between">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && (!formData.projectName || !formData.clientName)) ||
                  saving
                }
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function NewBVA() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    }>
      <TrialRestriction feature="BVA Creation">
        <NewBVAContent />
      </TrialRestriction>
    </Suspense>
  );
}
