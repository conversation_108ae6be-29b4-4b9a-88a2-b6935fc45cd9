(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1138:(e,s,a)=>{"use strict";a.d(s,{db:()=>n,j2:()=>u});var r=a(3915),t=a(6203),d=a(5317),i=a(858);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),u=(0,t.xI)(l),n=(0,d.aU)(l);(0,i.c7)(l)},3274:(e,s,a)=>{"use strict";a.d(s,{A:()=>o,AuthProvider:()=>n});var r=a(5155),t=a(2115),d=a(6203),i=a(1138),l=a(5317);let u=(0,t.createContext)(null),n=e=>{let{children:s}=e,[a,n]=(0,t.useState)(null),[o,c]=(0,t.useState)(!0),[m,x]=(0,t.useState)(!1);(0,t.useEffect)(()=>{let e=(0,d.hg)(i.j2,async e=>{if(n(e),e){var s;x((null==(s=(await (0,l.x7)((0,l.H9)(i.db,"users",e.uid))).data())?void 0:s.role)==="admin")}else x(!1);c(!1)});return()=>e()},[]);let h=async(e,s)=>{await (0,d.x9)(i.j2,e,s)},b=async(e,s)=>{let a=await (0,d.eJ)(i.j2,e,s);await (0,l.BN)((0,l.H9)(i.db,"users",a.user.uid),{email:a.user.email,role:"user",createdAt:new Date})},f=async()=>{await (0,d.CI)(i.j2)},p=async()=>{let e=new d.HF,s=await (0,d.df)(i.j2,e);(await (0,l.x7)((0,l.H9)(i.db,"users",s.user.uid))).exists()||await (0,l.BN)((0,l.H9)(i.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})},g=async()=>{let e=new d.sk,s=await (0,d.df)(i.j2,e);(await (0,l.x7)((0,l.H9)(i.db,"users",s.user.uid))).exists()||await (0,l.BN)((0,l.H9)(i.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})};return(0,r.jsx)(u.Provider,{value:{user:a,loading:o,signIn:h,signUp:b,logOut:f,signInWithGoogle:p,signInWithFacebook:g,isAdmin:m},children:s})},o=()=>{let e=(0,t.useContext)(u);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},5489:(e,s,a)=>{Promise.resolve().then(a.bind(a,8007))},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(s,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},8007:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var r=a(5155),t=a(2115),d=a(3274),i=a(5695),l=a(6874),u=a.n(l);function n(){let[e,s]=(0,t.useState)(""),[a,l]=(0,t.useState)(""),[n,o]=(0,t.useState)(""),{signIn:c,signInWithGoogle:m,signInWithFacebook:x}=(0,d.A)(),h=(0,i.useRouter)(),b=async s=>{s.preventDefault(),o("");try{await c(e,a),h.push("/dashboard")}catch(e){o(e.message)}},f=async()=>{try{await m(),h.push("/dashboard")}catch(e){o(e.message)}},p=async()=>{try{await x(),h.push("/dashboard")}catch(e){o(e.message)}};return(0,r.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,r.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Login to VALTICS AI"})}),n&&(0,r.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:n}),(0,r.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:a,onChange:e=>l(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Sign in"})})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,r.jsx)("button",{onClick:f,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Google"}),(0,r.jsx)("button",{onClick:p,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Facebook"})]})]}),(0,r.jsx)("div",{className:"text-center mt-4",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,r.jsx)(u(),{href:"/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"Register"})]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,288,874,441,684,358],()=>s(5489)),_N_E=e.O()}]);