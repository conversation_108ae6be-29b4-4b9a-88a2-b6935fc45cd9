{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { doc, getDoc, updateDoc, collection, query, where, getDocs, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { User, BVAInstance, Subscription } from '@/types';\n\nexport default function Profile() {\n  const { user, loading, logOut } = useAuth();\n  const { theme, setTheme } = useTheme();\n  const router = useRouter();\n\n  const [userProfile, setUserProfile] = useState<User | null>(null);\n  const [subscription, setSubscription] = useState<Subscription | null>(null);\n  const [bvaInstances, setBvaInstances] = useState<BVAInstance[]>([]);\n  const [loadingData, setLoadingData] = useState(true);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [updating, setUpdating] = useState(false);\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user) {\n      fetchProfileData();\n    }\n  }, [user]);\n\n  const fetchProfileData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch user profile\n      const userDoc = await getDoc(doc(db, 'users', user!.uid));\n      if (userDoc.exists()) {\n        setUserProfile({ id: userDoc.id, ...userDoc.data() } as User);\n      }\n\n      // Fetch subscription\n      const subscriptionQuery = query(\n        collection(db, 'subscriptions'),\n        where('userId', '==', user!.uid)\n      );\n      const subscriptionSnapshot = await getDocs(subscriptionQuery);\n      if (!subscriptionSnapshot.empty) {\n        const subscriptionData = subscriptionSnapshot.docs[0];\n        setSubscription({ id: subscriptionData.id, ...subscriptionData.data() } as Subscription);\n      }\n\n      // Fetch user's BVA instances\n      const bvaQuery = query(\n        collection(db, 'bvaInstances'),\n        where('userId', '==', user!.uid),\n        orderBy('updatedAt', 'desc')\n      );\n      const bvaSnapshot = await getDocs(bvaQuery);\n      const bvaData = bvaSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as BVAInstance[];\n      setBvaInstances(bvaData);\n\n    } catch (error) {\n      console.error('Error fetching profile data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  const updateProfile = async (updates: Partial<User>) => {\n    if (!user || !userProfile) return;\n\n    try {\n      setUpdating(true);\n      await updateDoc(doc(db, 'users', user.uid), updates);\n      setUserProfile({ ...userProfile, ...updates });\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Error updating profile. Please try again.');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user || !userProfile) {\n    return null;\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'in-progress':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'draft':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const renderProfile = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-slate-800 rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Account Information</h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Email Address\n              </label>\n              <input\n                type=\"email\"\n                value={user.email || ''}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400\"\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">Email cannot be changed</p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Account Type\n              </label>\n              <input\n                type=\"text\"\n                value={userProfile.role === 'admin' ? 'Administrator' : 'Standard User'}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Member Since\n              </label>\n              <input\n                type=\"text\"\n                value={new Date(userProfile.createdAt).toLocaleDateString()}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Total BVAs Created\n              </label>\n              <input\n                type=\"text\"\n                value={bvaInstances.length.toString()}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Theme Settings */}\n      <div className=\"bg-white dark:bg-slate-800 rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Appearance Settings</h3>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-slate-600 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">Theme Preference</h4>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Choose your preferred color scheme</p>\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => setTheme('light')}\n                  className={`px-3 py-2 rounded-md text-sm font-medium ${\n                    theme === 'light'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-200 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600'\n                  }`}\n                >\n                  Light\n                </button>\n                <button\n                  onClick={() => setTheme('dark')}\n                  className={`px-3 py-2 rounded-md text-sm font-medium ${\n                    theme === 'dark'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-200 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600'\n                  }`}\n                >\n                  Dark\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-slate-800 rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Account Actions</h3>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">Change Password</h4>\n                <p className=\"text-sm text-gray-500\">Update your account password</p>\n              </div>\n              <button className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700\">\n                Change Password\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">Download Data</h4>\n                <p className=\"text-sm text-gray-500\">Export all your BVA data</p>\n              </div>\n              <button className=\"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700\">\n                Export Data\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between p-4 border border-red-200 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-red-900\">Sign Out</h4>\n                <p className=\"text-sm text-red-600\">Sign out of your account</p>\n              </div>\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700\"\n              >\n                Sign Out\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSubscription = () => (\n    <div className=\"space-y-6\">\n      {subscription ? (\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Current Subscription</h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Plan Type\n                </label>\n                <div className=\"flex items-center\">\n                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                    subscription.type === 'enterprise' ? 'bg-purple-100 text-purple-800' :\n                    subscription.type === 'premium' ? 'bg-blue-100 text-blue-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {subscription.type.charAt(0).toUpperCase() + subscription.type.slice(1)}\n                  </span>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Status\n                </label>\n                <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                  subscription.status === 'active' ? 'bg-green-100 text-green-800' :\n                  subscription.status === 'cancelled' ? 'bg-red-100 text-red-800' :\n                  'bg-gray-100 text-gray-800'\n                }`}>\n                  {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}\n                </span>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Start Date\n                </label>\n                <p className=\"text-sm text-gray-900\">\n                  {new Date(subscription.startDate).toLocaleDateString()}\n                </p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  End Date\n                </label>\n                <p className=\"text-sm text-gray-900\">\n                  {new Date(subscription.endDate).toLocaleDateString()}\n                </p>\n              </div>\n\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Template Access\n                </label>\n                <p className=\"text-sm text-gray-900\">\n                  {subscription.accessibleTemplateIds.length} templates available\n                </p>\n              </div>\n            </div>\n\n            <div className=\"mt-6 flex space-x-4\">\n              <button className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700\">\n                Upgrade Plan\n              </button>\n              <button className=\"bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400\">\n                Manage Billing\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : (\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6 text-center\">\n            <div className=\"text-gray-400 text-6xl mb-4\">💳</div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Active Subscription</h3>\n            <p className=\"text-gray-600 mb-6\">\n              Subscribe to access premium templates and features.\n            </p>\n            <button className=\"bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700\">\n              View Subscription Plans\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderBVAs = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Your BVAs</h3>\n\n          {bvaInstances.length > 0 ? (\n            <div className=\"space-y-4\">\n              {bvaInstances.map((bva) => (\n                <div key={bva.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">{bva.name}</h4>\n                      <p className=\"text-sm text-gray-500\">Client: {bva.clientName}</p>\n                      <p className=\"text-xs text-gray-400\">\n                        Last updated: {new Date(bva.updatedAt).toLocaleDateString()}\n                      </p>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(bva.status)}`}>\n                        {bva.status}\n                      </span>\n                      <Link\n                        href={`/bva/${bva.id}`}\n                        className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                      >\n                        View\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-400 text-4xl mb-4\">📊</div>\n              <p className=\"text-gray-500\">No BVAs created yet.</p>\n              <Link\n                href=\"/bva/new\"\n                className=\"mt-4 inline-block bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700\"\n              >\n                Create Your First BVA\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-xl font-semibold text-gray-900\">\n                VALTICS AI\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                ← Back to Dashboard\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Profile & Settings</h1>\n            <p className=\"mt-2 text-gray-600\">\n              Manage your account settings and view your activity.\n            </p>\n          </div>\n\n          {/* Tabs */}\n          <div className=\"mb-8\">\n            <nav className=\"flex space-x-8\">\n              {[\n                { id: 'profile', name: 'Profile' },\n                { id: 'subscription', name: 'Subscription' },\n                { id: 'bvas', name: 'My BVAs' }\n              ].map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  {tab.name}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          {activeTab === 'profile' && renderProfile()}\n          {activeTab === 'subscription' && renderSubscription()}\n          {activeTab === 'bvas' && renderBVAs()}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AARA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB;QACvB,IAAI;YACF,eAAe;YAEf,qBAAqB;YACrB,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,KAAM,GAAG;YACvD,IAAI,QAAQ,MAAM,IAAI;gBACpB,eAAe;oBAAE,IAAI,QAAQ,EAAE;oBAAE,GAAG,QAAQ,IAAI,EAAE;gBAAC;YACrD;YAEA,qBAAqB;YACrB,MAAM,oBAAoB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAC5B,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,kBACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,KAAM,GAAG;YAEjC,MAAM,uBAAuB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YAC3C,IAAI,CAAC,qBAAqB,KAAK,EAAE;gBAC/B,MAAM,mBAAmB,qBAAqB,IAAI,CAAC,EAAE;gBACrD,gBAAgB;oBAAE,IAAI,iBAAiB,EAAE;oBAAE,GAAG,iBAAiB,IAAI,EAAE;gBAAC;YACxE;YAEA,6BAA6B;YAC7B,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACnB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,iBACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,KAAM,GAAG,GAC/B,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YAEvB,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YAClC,MAAM,UAAU,YAAY,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC3C,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,gBAAgB;QAElB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,QAAQ,CAAC,aAAa;QAE3B,IAAI;YACF,YAAY;YACZ,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG,GAAG;YAC5C,eAAe;gBAAE,GAAG,WAAW;gBAAE,GAAG,OAAO;YAAC;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,QAAQ,CAAC,aAAa;QACzB,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,kBACpB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAEvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,KAAK,KAAK,IAAI;gDACrB,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAG/D,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,YAAY,IAAI,KAAK,UAAU,kBAAkB;gDACxD,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,IAAI,KAAK,YAAY,SAAS,EAAE,kBAAkB;gDACzD,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,aAAa,MAAM,CAAC,QAAQ;gDACnC,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAEvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA4C;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,SAAS;oDACxB,WAAW,CAAC,yCAAyC,EACnD,UAAU,UACN,2BACA,4GACJ;8DACH;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,SAAS;oDACxB,WAAW,CAAC,yCAAyC,EACnD,UAAU,SACN,2BACA,4GACJ;8DACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAEvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAO,WAAU;0DAAoF;;;;;;;;;;;;kDAKxG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAO,WAAU;0DAAsF;;;;;;;;;;;;kDAK1G,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAuB;;;;;;;;;;;;0DAEtC,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUb,MAAM,qBAAqB,kBACzB,8OAAC;YAAI,WAAU;sBACZ,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAEvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,aAAa,IAAI,KAAK,eAAe,kCACrC,aAAa,IAAI,KAAK,YAAY,8BAClC,6BACA;0DACC,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;8CAK3E,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAK,WAAW,CAAC,2CAA2C,EAC3D,aAAa,MAAM,KAAK,WAAW,gCACnC,aAAa,MAAM,KAAK,cAAc,4BACtC,6BACA;sDACC,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;8CAI7E,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAE,WAAU;sDACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;;;;;;8CAIxD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAE,WAAU;sDACV,IAAI,KAAK,aAAa,OAAO,EAAE,kBAAkB;;;;;;;;;;;;8CAItD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAE,WAAU;;gDACV,aAAa,qBAAqB,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAAoF;;;;;;8CAGtG,8OAAC;oCAAO,WAAU;8CAAuF;;;;;;;;;;;;;;;;;;;;;;qCAO/G,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAO,WAAU;sCAA4E;;;;;;;;;;;;;;;;;;;;;;IASxG,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;wBAEtD,aAAa,MAAM,GAAG,kBACrB,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;oCAAiB,WAAU;8CAC1B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B,IAAI,IAAI;;;;;;kEACnD,8OAAC;wDAAE,WAAU;;4DAAwB;4DAAS,IAAI,UAAU;;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;;4DAAwB;4DACpB,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;0DAG7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,IAAI,MAAM,GAAG;kEACxF,IAAI,MAAM;;;;;;kEAEb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;wDACtB,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCAhBG,IAAI,EAAE;;;;;;;;;iDAyBpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAsC;;;;;;;;;;;0CAI1E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAMpC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,IAAI;wCAAW,MAAM;oCAAU;oCACjC;wCAAE,IAAI;wCAAgB,MAAM;oCAAe;oCAC3C;wCAAE,IAAI;wCAAQ,MAAM;oCAAU;iCAC/B,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;kDAED,IAAI,IAAI;uCARJ,IAAI,EAAE;;;;;;;;;;;;;;;wBAelB,cAAc,aAAa;wBAC3B,cAAc,kBAAkB;wBAChC,cAAc,UAAU;;;;;;;;;;;;;;;;;;AAKnC", "debugId": null}}]}