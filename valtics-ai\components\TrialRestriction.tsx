'use client';

import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

interface TrialRestrictionProps {
  children: React.ReactNode;
  feature: string;
  showUpgrade?: boolean;
}

export default function TrialRestriction({ 
  children, 
  feature, 
  showUpgrade = true 
}: TrialRestrictionProps) {
  const { user, canAccessPremiumFeatures } = useAuth();

  // If user can access premium features, show the content
  if (canAccessPremiumFeatures) {
    return <>{children}</>;
  }

  // If user is not logged in, redirect to login
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <div className="text-gray-400 dark:text-gray-500 text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Authentication Required
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Please log in to access {feature}.
          </p>
          <div className="space-y-3">
            <Link
              href="/login"
              className="w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block"
            >
              Log In
            </Link>
            <Link
              href="/register"
              className="w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block"
            >
              Start Free Trial
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Show trial expired message
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <div className="text-red-400 dark:text-red-300 text-6xl mb-4">⏰</div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Trial Expired
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Your free trial has ended. Upgrade to continue using {feature} and other premium features.
        </p>
        
        {/* Trial benefits */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            What you'll get with a subscription:
          </h3>
          <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
            <li>• Unlimited BVA creation</li>
            <li>• Access to all templates</li>
            <li>• Advanced analytics</li>
            <li>• Priority support</li>
            <li>• Export to multiple formats</li>
          </ul>
        </div>

        <div className="space-y-3">
          {showUpgrade && (
            <Link
              href="/pricing"
              className="w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block"
            >
              Upgrade Now
            </Link>
          )}
          <Link
            href="/dashboard"
            className="w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}

// Hook for checking trial access in components
export function useTrialAccess() {
  const { user, canAccessPremiumFeatures } = useAuth();

  return {
    hasAccess: canAccessPremiumFeatures,
    isTrialUser: user?.isTrialUser || false,
    isTrialExpired: user?.trialExpired || false,
    user
  };
}

// Component for inline trial restrictions (smaller version)
export function InlineTrialRestriction({ 
  children, 
  feature 
}: { 
  children: React.ReactNode; 
  feature: string; 
}) {
  const { canAccessPremiumFeatures } = useAuth();

  if (canAccessPremiumFeatures) {
    return <>{children}</>;
  }

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400 dark:text-yellow-300" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            <strong>Trial Required:</strong> {feature} requires an active subscription or trial.
          </p>
        </div>
        <div className="ml-4">
          <Link
            href="/pricing"
            className="text-sm bg-yellow-600 dark:bg-yellow-700 text-white px-3 py-1 rounded-md hover:bg-yellow-700 dark:hover:bg-yellow-800"
          >
            Upgrade
          </Link>
        </div>
      </div>
    </div>
  );
}
