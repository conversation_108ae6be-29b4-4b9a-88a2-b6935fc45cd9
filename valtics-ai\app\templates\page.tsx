'use client';

import { useEffect, useState, Suspense } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Template, Brand } from '@/types';
import Navigation from '@/components/Navigation';
import { useTrialAccess } from '@/components/TrialRestriction';

function TemplatesContent() {
  const { user, loading } = useAuth();
  const { hasAccess } = useTrialAccess();
  const router = useRouter();
  const searchParams = useSearchParams();
  const brandFilter = searchParams.get('brand');

  const [templates, setTemplates] = useState<Template[]>([]);
  const [brands, setBrands] = useState<Record<string, Brand>>({});
  const [loadingData, setLoadingData] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user) {
      fetchTemplatesData();
    }
  }, [user, brandFilter]);

  const fetchTemplatesData = async () => {
    try {
      setLoadingData(true);

      // Fetch brands first
      const brandsQuery = query(
        collection(db, 'brands'),
        where('isActive', '==', true)
      );
      const brandsSnapshot = await getDocs(brandsQuery);
      const brandsData: Record<string, Brand> = {};
      brandsSnapshot.docs.forEach(doc => {
        brandsData[doc.id] = { id: doc.id, ...doc.data() } as Brand;
      });
      setBrands(brandsData);

      // Fetch templates
      let templatesQuery;
      if (brandFilter) {
        templatesQuery = query(
          collection(db, 'templates'),
          where('brandId', '==', brandFilter),
          where('isActive', '==', true),
          orderBy('name', 'asc')
        );
      } else {
        templatesQuery = query(
          collection(db, 'templates'),
          where('isActive', '==', true),
          orderBy('name', 'asc')
        );
      }

      const templatesSnapshot = await getDocs(templatesQuery);
      const templatesData = templatesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Template[];
      setTemplates(templatesData);

    } catch (error) {
      console.error('Error fetching templates data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const categories = Array.from(new Set(templates.map(t => t.category)));

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-xl text-gray-900 dark:text-white">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation title="VALTICS AI" />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">BVA Templates</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-300">
              Choose from our collection of professional Business Value Analysis templates.
              {brandFilter && brands[brandFilter] && (
                <span className="ml-2 text-blue-600 dark:text-blue-400">
                  Showing templates for {brands[brandFilter].name}
                </span>
              )}
            </p>
          </div>

          {/* Filters */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Category Filter */}
            <div className="sm:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {brandFilter && (
              <Link
                href="/templates"
                className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 text-sm font-medium"
              >
                Clear Brand Filter
              </Link>
            )}
          </div>

          {/* Templates Grid */}
          {filteredTemplates.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => (
                <div key={template.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    {/* Brand info */}
                    {brands[template.brandId] && (
                      <div className="flex items-center mb-3">
                        {brands[template.brandId].logoUrl && (
                          <img
                            src={brands[template.brandId].logoUrl}
                            alt={brands[template.brandId].name}
                            className="h-6 w-auto mr-2"
                          />
                        )}
                        <span className="text-sm text-gray-500 dark:text-gray-400">{brands[template.brandId].name}</span>
                      </div>
                    )}

                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      {template.name}
                    </h3>

                    <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                      {template.description}
                    </p>

                    <div className="flex items-center justify-between mb-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                        {template.category}
                      </span>
                      <span className="text-lg font-semibold text-green-600 dark:text-green-400">
                        ${template.price}
                      </span>
                    </div>

                    {/* File types available */}
                    <div className="mb-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Available formats:</p>
                      <div className="flex space-x-2">
                        {template.fileUrls.pdf && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                            PDF
                          </span>
                        )}
                        {template.fileUrls.ppt && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200">
                            PPT
                          </span>
                        )}
                        {template.fileUrls.excel && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                            Excel
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Link
                        href={`/templates/${template.id}`}
                        className="flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 text-center"
                      >
                        View Details
                      </Link>
                      {hasAccess ? (
                        <Link
                          href={`/bva/new?template=${template.id}`}
                          className="flex-1 bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-800 text-center"
                        >
                          Use Template
                        </Link>
                      ) : (
                        <Link
                          href="/pricing"
                          className="flex-1 bg-yellow-600 dark:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700 dark:hover:bg-yellow-800 text-center"
                        >
                          Upgrade to Use
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 dark:text-gray-500 text-6xl mb-4">📄</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No templates found</h3>
              <p className="text-gray-600 dark:text-gray-300">
                {searchTerm || selectedCategory !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Check back later for new templates.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function Templates() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    }>
      <TemplatesContent />
    </Suspense>
  );
}
