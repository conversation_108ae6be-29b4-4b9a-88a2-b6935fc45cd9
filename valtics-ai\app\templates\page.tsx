'use client';

import { useEffect, useState, Suspense } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Template, Brand } from '@/types';

function TemplatesContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const brandFilter = searchParams.get('brand');

  const [templates, setTemplates] = useState<Template[]>([]);
  const [brands, setBrands] = useState<Record<string, Brand>>({});
  const [loadingData, setLoadingData] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user) {
      fetchTemplatesData();
    }
  }, [user, brandFilter]);

  const fetchTemplatesData = async () => {
    try {
      setLoadingData(true);

      // Fetch brands first
      const brandsQuery = query(
        collection(db, 'brands'),
        where('isActive', '==', true)
      );
      const brandsSnapshot = await getDocs(brandsQuery);
      const brandsData: Record<string, Brand> = {};
      brandsSnapshot.docs.forEach(doc => {
        brandsData[doc.id] = { id: doc.id, ...doc.data() } as Brand;
      });
      setBrands(brandsData);

      // Fetch templates
      let templatesQuery;
      if (brandFilter) {
        templatesQuery = query(
          collection(db, 'templates'),
          where('brandId', '==', brandFilter),
          where('isActive', '==', true),
          orderBy('name', 'asc')
        );
      } else {
        templatesQuery = query(
          collection(db, 'templates'),
          where('isActive', '==', true),
          orderBy('name', 'asc')
        );
      }

      const templatesSnapshot = await getDocs(templatesQuery);
      const templatesData = templatesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Template[];
      setTemplates(templatesData);

    } catch (error) {
      console.error('Error fetching templates data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const categories = Array.from(new Set(templates.map(t => t.category)));

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-xl font-semibold text-gray-900">
                VALTICS AI
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Dashboard
              </Link>
              <Link
                href="/brands"
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Brands
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">BVA Templates</h1>
            <p className="mt-2 text-gray-600">
              Choose from our collection of professional Business Value Analysis templates.
              {brandFilter && brands[brandFilter] && (
                <span className="ml-2 text-blue-600">
                  Showing templates for {brands[brandFilter].name}
                </span>
              )}
            </p>
          </div>

          {/* Filters */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Category Filter */}
            <div className="sm:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {brandFilter && (
              <Link
                href="/templates"
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium"
              >
                Clear Brand Filter
              </Link>
            )}
          </div>

          {/* Templates Grid */}
          {filteredTemplates.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => (
                <div key={template.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    {/* Brand info */}
                    {brands[template.brandId] && (
                      <div className="flex items-center mb-3">
                        {brands[template.brandId].logoUrl && (
                          <img
                            src={brands[template.brandId].logoUrl}
                            alt={brands[template.brandId].name}
                            className="h-6 w-auto mr-2"
                          />
                        )}
                        <span className="text-sm text-gray-500">{brands[template.brandId].name}</span>
                      </div>
                    )}

                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {template.name}
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {template.description}
                    </p>

                    <div className="flex items-center justify-between mb-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {template.category}
                      </span>
                      <span className="text-lg font-semibold text-green-600">
                        ${template.price}
                      </span>
                    </div>

                    {/* File types available */}
                    <div className="mb-4">
                      <p className="text-sm text-gray-500 mb-2">Available formats:</p>
                      <div className="flex space-x-2">
                        {template.fileUrls.pdf && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                            PDF
                          </span>
                        )}
                        {template.fileUrls.ppt && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 text-orange-800">
                            PPT
                          </span>
                        )}
                        {template.fileUrls.excel && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                            Excel
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Link
                        href={`/templates/${template.id}`}
                        className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 text-center"
                      >
                        View Details
                      </Link>
                      <Link
                        href={`/bva/new?template=${template.id}`}
                        className="flex-1 bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 text-center"
                      >
                        Use Template
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📄</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
              <p className="text-gray-600">
                {searchTerm || selectedCategory !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Check back later for new templates.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function Templates() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    }>
      <TemplatesContent />
    </Suspense>
  );
}
