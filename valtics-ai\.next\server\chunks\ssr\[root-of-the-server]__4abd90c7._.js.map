{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,sCAAsC;QACtC,IAAI,CAAC,cAAc;YACjB,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;gBACT,WAAW;YACb,OAAO;gBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;gBACT,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getTrialStatusMessage } from '@/lib/trial';\n\nexport default function TrialBanner() {\n  const { user } = useAuth();\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  // Don't show banner if user is not a trial user, is admin, or banner is dismissed\n  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {\n    return null;\n  }\n\n  const { message, type, daysRemaining } = getTrialStatusMessage(user);\n\n  // Don't show banner if no message\n  if (!message) {\n    return null;\n  }\n\n  const getBannerStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';\n      case 'warning':\n        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';\n      default:\n        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';\n    }\n  };\n\n  const getButtonStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';\n      case 'warning':\n        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';\n      default:\n        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';\n    }\n  };\n\n  const getIconStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'text-red-400 dark:text-red-300';\n      case 'warning':\n        return 'text-yellow-400 dark:text-yellow-300';\n      default:\n        return 'text-blue-400 dark:text-blue-300';\n    }\n  };\n\n  return (\n    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {type === 'error' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : type === 'warning' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            )}\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium\">\n              {message}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/pricing\"\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}\n          >\n            Upgrade Now\n          </Link>\n          \n          <button\n            onClick={() => setIsDismissed(true)}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            aria-label=\"Dismiss banner\"\n          >\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Compact version for navigation bar\nexport function TrialIndicator() {\n  const { user } = useAuth();\n\n  if (!user || !user.isTrialUser || user.role === 'admin') {\n    return null;\n  }\n\n  const { daysRemaining } = getTrialStatusMessage(user);\n\n  if (daysRemaining <= 0) {\n    return (\n      <Link\n        href=\"/pricing\"\n        className=\"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n      >\n        Trial Expired\n      </Link>\n    );\n  }\n\n  const getIndicatorStyles = () => {\n    if (daysRemaining <= 3) {\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';\n    }\n    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';\n  };\n\n  return (\n    <Link\n      href=\"/pricing\"\n      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}\n    >\n      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kFAAkF;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,WAAW,aAAa;QACtE,OAAO;IACT;IAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAE/D,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,mBAAmB;kBACnD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;uCAE9P,SAAS,0BACX,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoN,UAAS;;;;;;;;;;qDAG1P,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmI,UAAS;;;;;;;;;;;;;;;;sCAI7K,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,2DAA2D,EAAE,mBAAmB;sCAC7F;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;0CAChD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,SAAS;QACvD,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAEhD,IAAI,iBAAiB,GAAG;QACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;sBACX;;;;;;IAIL;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB,GAAG;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,sBAAsB;;YAEhG;YAAc;YAAK,kBAAkB,IAAI,KAAK;YAAI;;;;;;;AAGzD", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport ThemeToggle from './ThemeToggle';\nimport { TrialIndicator } from './TrialBanner';\n\ninterface NavigationProps {\n  title?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n}\n\nexport default function Navigation({\n  title = 'VALTICS AI',\n  showBackButton = false,\n  backUrl = '/dashboard',\n  backText = '← Back to Dashboard'\n}: NavigationProps) {\n  const { user, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"VALTICS AI Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {title}\n              </span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {backText}\n              </Link>\n            )}\n\n            {!showBackButton && (\n              <>\n                <Link\n                  href=\"/brands\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Brands\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Templates\n                </Link>\n                {isAdmin && (\n                  <Link\n                    href=\"/admin\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Admin\n                  </Link>\n                )}\n                <Link\n                  href=\"/profile\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Profile\n                </Link>\n              </>\n            )}\n\n            {/* Trial Indicator */}\n            <TrialIndicator />\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            <button\n              onClick={handleLogout}\n              className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAgBe,SAAS,WAAW,EACjC,QAAQ,YAAY,EACpB,iBAAiB,KAAK,EACtB,UAAU,YAAY,EACtB,WAAW,qBAAqB,EAChB;IAChB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKP,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;0CAET;;;;;;4BAIJ,CAAC,gCACA;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAOL,8OAAC,0HAAA,CAAA,iBAAc;;;;;0CAGf,8OAAC,0HAAA,CAAA,UAAW;;;;;0CAEZ,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialRestriction.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface TrialRestrictionProps {\n  children: React.ReactNode;\n  feature: string;\n  showUpgrade?: boolean;\n}\n\nexport default function TrialRestriction({ \n  children, \n  feature, \n  showUpgrade = true \n}: TrialRestrictionProps) {\n  const { user, canAccessPremiumFeatures } = useAuth();\n\n  // If user can access premium features, show the content\n  if (canAccessPremiumFeatures) {\n    return <>{children}</>;\n  }\n\n  // If user is not logged in, redirect to login\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center\">\n          <div className=\"text-gray-400 dark:text-gray-500 text-6xl mb-4\">🔒</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Authentication Required\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n            Please log in to access {feature}.\n          </p>\n          <div className=\"space-y-3\">\n            <Link\n              href=\"/login\"\n              className=\"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block\"\n            >\n              Log In\n            </Link>\n            <Link\n              href=\"/register\"\n              className=\"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block\"\n            >\n              Start Free Trial\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show trial expired message\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n      <div className=\"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center\">\n        <div className=\"text-red-400 dark:text-red-300 text-6xl mb-4\">⏰</div>\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n          Trial Expired\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n          Your free trial has ended. Upgrade to continue using {feature} and other premium features.\n        </p>\n        \n        {/* Trial benefits */}\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6\">\n          <h3 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n            What you'll get with a subscription:\n          </h3>\n          <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n            <li>• Unlimited BVA creation</li>\n            <li>• Access to all templates</li>\n            <li>• Advanced analytics</li>\n            <li>• Priority support</li>\n            <li>• Export to multiple formats</li>\n          </ul>\n        </div>\n\n        <div className=\"space-y-3\">\n          {showUpgrade && (\n            <Link\n              href=\"/pricing\"\n              className=\"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block\"\n            >\n              Upgrade Now\n            </Link>\n          )}\n          <Link\n            href=\"/dashboard\"\n            className=\"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block\"\n          >\n            Back to Dashboard\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Hook for checking trial access in components\nexport function useTrialAccess() {\n  const { user, canAccessPremiumFeatures } = useAuth();\n\n  return {\n    hasAccess: canAccessPremiumFeatures,\n    isTrialUser: user?.isTrialUser || false,\n    isTrialExpired: user?.trialExpired || false,\n    user\n  };\n}\n\n// Component for inline trial restrictions (smaller version)\nexport function InlineTrialRestriction({ \n  children, \n  feature \n}: { \n  children: React.ReactNode; \n  feature: string; \n}) {\n  const { canAccessPremiumFeatures } = useAuth();\n\n  if (canAccessPremiumFeatures) {\n    return <>{children}</>;\n  }\n\n  return (\n    <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n      <div className=\"flex items-center\">\n        <div className=\"flex-shrink-0\">\n          <svg className=\"h-5 w-5 text-yellow-400 dark:text-yellow-300\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n        <div className=\"ml-3 flex-1\">\n          <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n            <strong>Trial Required:</strong> {feature} requires an active subscription or trial.\n          </p>\n        </div>\n        <div className=\"ml-4\">\n          <Link\n            href=\"/pricing\"\n            className=\"text-sm bg-yellow-600 dark:bg-yellow-700 text-white px-3 py-1 rounded-md hover:bg-yellow-700 dark:hover:bg-yellow-800\"\n          >\n            Upgrade\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,iBAAiB,EACvC,QAAQ,EACR,OAAO,EACP,cAAc,IAAI,EACI;IACtB,MAAM,EAAE,IAAI,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEjD,wDAAwD;IACxD,IAAI,0BAA0B;QAC5B,qBAAO;sBAAG;;IACZ;IAEA,8CAA8C;IAC9C,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAiD;;;;;;kCAChE,8OAAC;wBAAG,WAAU;kCAAwD;;;;;;kCAGtE,8OAAC;wBAAE,WAAU;;4BAAwC;4BAC1B;4BAAQ;;;;;;;kCAEnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,6BAA6B;IAC7B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAA+C;;;;;;8BAC9D,8OAAC;oBAAG,WAAU;8BAAwD;;;;;;8BAGtE,8OAAC;oBAAE,WAAU;;wBAAwC;wBACG;wBAAQ;;;;;;;8BAIhE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;8BAIR,8OAAC;oBAAI,WAAU;;wBACZ,6BACC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAIH,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEjD,OAAO;QACL,WAAW;QACX,aAAa,MAAM,eAAe;QAClC,gBAAgB,MAAM,gBAAgB;QACtC;IACF;AACF;AAGO,SAAS,uBAAuB,EACrC,QAAQ,EACR,OAAO,EAIR;IACC,MAAM,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAE3C,IAAI,0BAA0B;QAC5B,qBAAO;sBAAG;;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAA+C,SAAQ;wBAAY,MAAK;kCACrF,cAAA,8OAAC;4BAAK,UAAS;4BAAU,GAAE;4BAA0N,UAAS;;;;;;;;;;;;;;;;8BAGlQ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;0CAAO;;;;;;4BAAwB;4BAAE;4BAAQ;;;;;;;;;;;;8BAG9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/templates/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState, Suspense } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { collection, query, where, getDocs, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Template, Brand } from '@/types';\nimport Navigation from '@/components/Navigation';\nimport { useTrialAccess } from '@/components/TrialRestriction';\n\nfunction TemplatesContent() {\n  const { user, loading } = useAuth();\n  const { hasAccess } = useTrialAccess();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const brandFilter = searchParams.get('brand');\n\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [brands, setBrands] = useState<Record<string, Brand>>({});\n  const [loadingData, setLoadingData] = useState(true);\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user) {\n      fetchTemplatesData();\n    }\n  }, [user, brandFilter]);\n\n  const fetchTemplatesData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch brands first\n      const brandsQuery = query(\n        collection(db, 'brands'),\n        where('isActive', '==', true)\n      );\n      const brandsSnapshot = await getDocs(brandsQuery);\n      const brandsData: Record<string, Brand> = {};\n      brandsSnapshot.docs.forEach(doc => {\n        brandsData[doc.id] = { id: doc.id, ...doc.data() } as Brand;\n      });\n      setBrands(brandsData);\n\n      // Fetch templates\n      let templatesQuery;\n      if (brandFilter) {\n        templatesQuery = query(\n          collection(db, 'templates'),\n          where('brandId', '==', brandFilter),\n          where('isActive', '==', true),\n          orderBy('name', 'asc')\n        );\n      } else {\n        templatesQuery = query(\n          collection(db, 'templates'),\n          where('isActive', '==', true),\n          orderBy('name', 'asc')\n        );\n      }\n\n      const templatesSnapshot = await getDocs(templatesQuery);\n      const templatesData = templatesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Template[];\n      setTemplates(templatesData);\n\n    } catch (error) {\n      console.error('Error fetching templates data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const filteredTemplates = templates.filter(template => {\n    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;\n    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         template.description.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n\n  const categories = Array.from(new Set(templates.map(t => t.category)));\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-xl text-gray-900 dark:text-white\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Navigation title=\"VALTICS AI\" />\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">BVA Templates</h1>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-300\">\n              Choose from our collection of professional Business Value Analysis templates.\n              {brandFilter && brands[brandFilter] && (\n                <span className=\"ml-2 text-blue-600 dark:text-blue-400\">\n                  Showing templates for {brands[brandFilter].name}\n                </span>\n              )}\n            </p>\n          </div>\n\n          {/* Filters */}\n          <div className=\"mb-6 flex flex-col sm:flex-row gap-4\">\n            {/* Search */}\n            <div className=\"flex-1\">\n              <input\n                type=\"text\"\n                placeholder=\"Search templates...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n\n            {/* Category Filter */}\n            <div className=\"sm:w-48\">\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"all\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>\n                    {category}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {brandFilter && (\n              <Link\n                href=\"/templates\"\n                className=\"px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 text-sm font-medium\"\n              >\n                Clear Brand Filter\n              </Link>\n            )}\n          </div>\n\n          {/* Templates Grid */}\n          {filteredTemplates.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {filteredTemplates.map((template) => (\n                <div key={template.id} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                  <div className=\"p-6\">\n                    {/* Brand info */}\n                    {brands[template.brandId] && (\n                      <div className=\"flex items-center mb-3\">\n                        {brands[template.brandId].logoUrl && (\n                          <img\n                            src={brands[template.brandId].logoUrl}\n                            alt={brands[template.brandId].name}\n                            className=\"h-6 w-auto mr-2\"\n                          />\n                        )}\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400\">{brands[template.brandId].name}</span>\n                      </div>\n                    )}\n\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                      {template.name}\n                    </h3>\n\n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\n                      {template.description}\n                    </p>\n\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\">\n                        {template.category}\n                      </span>\n                      <span className=\"text-lg font-semibold text-green-600 dark:text-green-400\">\n                        ${template.price}\n                      </span>\n                    </div>\n\n                    {/* File types available */}\n                    <div className=\"mb-4\">\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-2\">Available formats:</p>\n                      <div className=\"flex space-x-2\">\n                        {template.fileUrls.pdf && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200\">\n                            PDF\n                          </span>\n                        )}\n                        {template.fileUrls.ppt && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200\">\n                            PPT\n                          </span>\n                        )}\n                        {template.fileUrls.excel && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\">\n                            Excel\n                          </span>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Actions */}\n                    <div className=\"flex space-x-2\">\n                      <Link\n                        href={`/templates/${template.id}`}\n                        className=\"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 text-center\"\n                      >\n                        View Details\n                      </Link>\n                      {hasAccess ? (\n                        <Link\n                          href={`/bva/new?template=${template.id}`}\n                          className=\"flex-1 bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-800 text-center\"\n                        >\n                          Use Template\n                        </Link>\n                      ) : (\n                        <Link\n                          href=\"/pricing\"\n                          className=\"flex-1 bg-yellow-600 dark:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700 dark:hover:bg-yellow-800 text-center\"\n                        >\n                          Upgrade to Use\n                        </Link>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 dark:text-gray-500 text-6xl mb-4\">📄</div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">No templates found</h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                {searchTerm || selectedCategory !== 'all'\n                  ? 'Try adjusting your search or filter criteria.'\n                  : 'Check back later for new templates.'\n                }\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Templates() {\n  return (\n    <Suspense fallback={\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    }>\n      <TemplatesContent />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAVA;;;;;;;;;;AAYA,SAAS;IACP,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,aAAa,GAAG,CAAC;IAErC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,qBAAqB;QACzB,IAAI;YACF,eAAe;YAEf,qBAAqB;YACrB,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACtB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM;YAE1B,MAAM,iBAAiB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACrC,MAAM,aAAoC,CAAC;YAC3C,eAAe,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC1B,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG;oBAAE,IAAI,IAAI,EAAE;oBAAE,GAAG,IAAI,IAAI,EAAE;gBAAC;YACnD;YACA,UAAU;YAEV,kBAAkB;YAClB,IAAI;YACJ,IAAI,aAAa;gBACf,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACnB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,cACvB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;YAEpB,OAAO;gBACL,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACnB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;YAEpB;YAEA,MAAM,oBAAoB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACxC,MAAM,gBAAgB,kBAAkB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACvD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,aAAa;QAEf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,kBAAkB,qBAAqB,SAAS,SAAS,QAAQ,KAAK;QAC5E,MAAM,gBAAgB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACvF,OAAO,mBAAmB;IAC5B;IAEA,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAEnE,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAwC;;;;;;;;;;;IAG7D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,UAAU;gBAAC,OAAM;;;;;;0BAGlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;;wCAAwC;wCAElD,eAAe,MAAM,CAAC,YAAY,kBACjC,8OAAC;4CAAK,WAAU;;gDAAwC;gDAC/B,MAAM,CAAC,YAAY,CAAC,IAAI;;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oDAAsB,OAAO;8DAC3B;mDADU;;;;;;;;;;;;;;;;gCAOlB,6BACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;wBAOJ,kBAAkB,MAAM,GAAG,kBAC1B,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oCAAsB,WAAU;8CAC/B,cAAA,8OAAC;wCAAI,WAAU;;4CAEZ,MAAM,CAAC,SAAS,OAAO,CAAC,kBACvB,8OAAC;gDAAI,WAAU;;oDACZ,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,OAAO,kBAC/B,8OAAC;wDACC,KAAK,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,OAAO;wDACrC,KAAK,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,IAAI;wDAClC,WAAU;;;;;;kEAGd,8OAAC;wDAAK,WAAU;kEAA4C,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,IAAI;;;;;;;;;;;;0DAI7F,8OAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAGhB,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAGvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,SAAS,QAAQ;;;;;;kEAEpB,8OAAC;wDAAK,WAAU;;4DAA2D;4DACvE,SAAS,KAAK;;;;;;;;;;;;;0DAKpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAgD;;;;;;kEAC7D,8OAAC;wDAAI,WAAU;;4DACZ,SAAS,QAAQ,CAAC,GAAG,kBACpB,8OAAC;gEAAK,WAAU;0EAA2H;;;;;;4DAI5I,SAAS,QAAQ,CAAC,GAAG,kBACpB,8OAAC;gEAAK,WAAU;0EAAuI;;;;;;4DAIxJ,SAAS,QAAQ,CAAC,KAAK,kBACtB,8OAAC;gEAAK,WAAU;0EAAmI;;;;;;;;;;;;;;;;;;0DAQzJ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;wDACjC,WAAU;kEACX;;;;;;oDAGA,0BACC,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,kBAAkB,EAAE,SAAS,EAAE,EAAE;wDACxC,WAAU;kEACX;;;;;6EAID,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCA1EC,SAAS,EAAE;;;;;;;;;iDAoFzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAiD;;;;;;8CAChE,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CACV,cAAc,qBAAqB,QAChC,kDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;AAEe,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBACR,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;kBAG3B,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}