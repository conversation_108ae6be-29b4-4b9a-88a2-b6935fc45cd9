{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/templates/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState, Suspense } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { collection, query, where, getDocs, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Template, Brand } from '@/types';\n\nfunction TemplatesContent() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const brandFilter = searchParams.get('brand');\n\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [brands, setBrands] = useState<Record<string, Brand>>({});\n  const [loadingData, setLoadingData] = useState(true);\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user) {\n      fetchTemplatesData();\n    }\n  }, [user, brandFilter]);\n\n  const fetchTemplatesData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch brands first\n      const brandsQuery = query(\n        collection(db, 'brands'),\n        where('isActive', '==', true)\n      );\n      const brandsSnapshot = await getDocs(brandsQuery);\n      const brandsData: Record<string, Brand> = {};\n      brandsSnapshot.docs.forEach(doc => {\n        brandsData[doc.id] = { id: doc.id, ...doc.data() } as Brand;\n      });\n      setBrands(brandsData);\n\n      // Fetch templates\n      let templatesQuery;\n      if (brandFilter) {\n        templatesQuery = query(\n          collection(db, 'templates'),\n          where('brandId', '==', brandFilter),\n          where('isActive', '==', true),\n          orderBy('name', 'asc')\n        );\n      } else {\n        templatesQuery = query(\n          collection(db, 'templates'),\n          where('isActive', '==', true),\n          orderBy('name', 'asc')\n        );\n      }\n\n      const templatesSnapshot = await getDocs(templatesQuery);\n      const templatesData = templatesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Template[];\n      setTemplates(templatesData);\n\n    } catch (error) {\n      console.error('Error fetching templates data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const filteredTemplates = templates.filter(template => {\n    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;\n    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         template.description.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n\n  const categories = Array.from(new Set(templates.map(t => t.category)));\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-xl font-semibold text-gray-900\">\n                VALTICS AI\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Dashboard\n              </Link>\n              <Link\n                href=\"/brands\"\n                className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Brands\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">BVA Templates</h1>\n            <p className=\"mt-2 text-gray-600\">\n              Choose from our collection of professional Business Value Analysis templates.\n              {brandFilter && brands[brandFilter] && (\n                <span className=\"ml-2 text-blue-600\">\n                  Showing templates for {brands[brandFilter].name}\n                </span>\n              )}\n            </p>\n          </div>\n\n          {/* Filters */}\n          <div className=\"mb-6 flex flex-col sm:flex-row gap-4\">\n            {/* Search */}\n            <div className=\"flex-1\">\n              <input\n                type=\"text\"\n                placeholder=\"Search templates...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n\n            {/* Category Filter */}\n            <div className=\"sm:w-48\">\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"all\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>\n                    {category}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {brandFilter && (\n              <Link\n                href=\"/templates\"\n                className=\"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium\"\n              >\n                Clear Brand Filter\n              </Link>\n            )}\n          </div>\n\n          {/* Templates Grid */}\n          {filteredTemplates.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {filteredTemplates.map((template) => (\n                <div key={template.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                  <div className=\"p-6\">\n                    {/* Brand info */}\n                    {brands[template.brandId] && (\n                      <div className=\"flex items-center mb-3\">\n                        {brands[template.brandId].logoUrl && (\n                          <img\n                            src={brands[template.brandId].logoUrl}\n                            alt={brands[template.brandId].name}\n                            className=\"h-6 w-auto mr-2\"\n                          />\n                        )}\n                        <span className=\"text-sm text-gray-500\">{brands[template.brandId].name}</span>\n                      </div>\n                    )}\n\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                      {template.name}\n                    </h3>\n\n                    <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                      {template.description}\n                    </p>\n\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        {template.category}\n                      </span>\n                      <span className=\"text-lg font-semibold text-green-600\">\n                        ${template.price}\n                      </span>\n                    </div>\n\n                    {/* File types available */}\n                    <div className=\"mb-4\">\n                      <p className=\"text-sm text-gray-500 mb-2\">Available formats:</p>\n                      <div className=\"flex space-x-2\">\n                        {template.fileUrls.pdf && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800\">\n                            PDF\n                          </span>\n                        )}\n                        {template.fileUrls.ppt && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 text-orange-800\">\n                            PPT\n                          </span>\n                        )}\n                        {template.fileUrls.excel && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800\">\n                            Excel\n                          </span>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Actions */}\n                    <div className=\"flex space-x-2\">\n                      <Link\n                        href={`/templates/${template.id}`}\n                        className=\"flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 text-center\"\n                      >\n                        View Details\n                      </Link>\n                      <Link\n                        href={`/bva/new?template=${template.id}`}\n                        className=\"flex-1 bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 text-center\"\n                      >\n                        Use Template\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 text-6xl mb-4\">📄</div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No templates found</h3>\n              <p className=\"text-gray-600\">\n                {searchTerm || selectedCategory !== 'all'\n                  ? 'Try adjusting your search or filter criteria.'\n                  : 'Check back later for new templates.'\n                }\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Templates() {\n  return (\n    <Suspense fallback={\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    }>\n      <TemplatesContent />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AAUA,SAAS;IACP,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,aAAa,GAAG,CAAC;IAErC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,qBAAqB;QACzB,IAAI;YACF,eAAe;YAEf,qBAAqB;YACrB,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACtB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM;YAE1B,MAAM,iBAAiB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACrC,MAAM,aAAoC,CAAC;YAC3C,eAAe,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC1B,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG;oBAAE,IAAI,IAAI,EAAE;oBAAE,GAAG,IAAI,IAAI,EAAE;gBAAC;YACnD;YACA,UAAU;YAEV,kBAAkB;YAClB,IAAI;YACJ,IAAI,aAAa;gBACf,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACnB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,cACvB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;YAEpB,OAAO;gBACL,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACnB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;YAEpB;YAEA,MAAM,oBAAoB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACxC,MAAM,gBAAgB,kBAAkB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACvD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,aAAa;QAEf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,kBAAkB,qBAAqB,SAAS,SAAS,QAAQ,KAAK;QAC5E,MAAM,gBAAgB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACvF,OAAO,mBAAmB;IAC5B;IAEA,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAEnE,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAsC;;;;;;;;;;;0CAI1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;;wCAAqB;wCAE/B,eAAe,MAAM,CAAC,YAAY,kBACjC,8OAAC;4CAAK,WAAU;;gDAAqB;gDACZ,MAAM,CAAC,YAAY,CAAC,IAAI;;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oDAAsB,OAAO;8DAC3B;mDADU;;;;;;;;;;;;;;;;gCAOlB,6BACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;wBAOJ,kBAAkB,MAAM,GAAG,kBAC1B,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oCAAsB,WAAU;8CAC/B,cAAA,8OAAC;wCAAI,WAAU;;4CAEZ,MAAM,CAAC,SAAS,OAAO,CAAC,kBACvB,8OAAC;gDAAI,WAAU;;oDACZ,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,OAAO,kBAC/B,8OAAC;wDACC,KAAK,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,OAAO;wDACrC,KAAK,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,IAAI;wDAClC,WAAU;;;;;;kEAGd,8OAAC;wDAAK,WAAU;kEAAyB,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC,IAAI;;;;;;;;;;;;0DAI1E,8OAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAGhB,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAGvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,SAAS,QAAQ;;;;;;kEAEpB,8OAAC;wDAAK,WAAU;;4DAAuC;4DACnD,SAAS,KAAK;;;;;;;;;;;;;0DAKpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDAAI,WAAU;;4DACZ,SAAS,QAAQ,CAAC,GAAG,kBACpB,8OAAC;gEAAK,WAAU;0EAAyF;;;;;;4DAI1G,SAAS,QAAQ,CAAC,GAAG,kBACpB,8OAAC;gEAAK,WAAU;0EAA+F;;;;;;4DAIhH,SAAS,QAAQ,CAAC,KAAK,kBACtB,8OAAC;gEAAK,WAAU;0EAA6F;;;;;;;;;;;;;;;;;;0DAQnH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;wDACjC,WAAU;kEACX;;;;;;kEAGD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,kBAAkB,EAAE,SAAS,EAAE,EAAE;wDACxC,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCAlEG,SAAS,EAAE;;;;;;;;;iDA2EzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CACV,cAAc,qBAAqB,QAChC,kDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;AAEe,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBACR,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;kBAG3B,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}