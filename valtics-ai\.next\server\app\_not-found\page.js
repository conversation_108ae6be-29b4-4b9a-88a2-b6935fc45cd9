(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27732:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>d});var s=r(60687),i=r(43210),n=r(19978),o=r(56304),a=r(75535);let u=(0,i.createContext)(null),d=({children:e})=>{let[t,r]=(0,i.useState)(null),[d,l]=(0,i.useState)(!0),[c,p]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=(0,n.hg)(o.j2,async e=>{if(r(e),e){let t=await (0,a.x7)((0,a.H9)(o.db,"users",e.uid));p(t.data()?.role==="admin")}else p(!1);l(!1)});return()=>e()},[]);let h=async(e,t)=>{await (0,n.x9)(o.j2,e,t)},v=async(e,t)=>{let r=await (0,n.eJ)(o.j2,e,t);await (0,a.BN)((0,a.H9)(o.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},m=async()=>{await (0,n.CI)(o.j2)},x=async()=>{let e=new n.HF,t=await (0,n.df)(o.j2,e);(await (0,a.x7)((0,a.H9)(o.db,"users",t.user.uid))).exists()||await (0,a.BN)((0,a.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},b=async()=>{let e=new n.sk,t=await (0,n.df)(o.j2,e);(await (0,a.x7)((0,a.H9)(o.db,"users",t.user.uid))).exists()||await (0,a.BN)((0,a.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(u.Provider,{value:{user:t,loading:d,signIn:h,signUp:v,logOut:m,signInWithGoogle:x,signInWithFacebook:b,isAdmin:c},children:e})},l=()=>{let e=(0,i.useContext)(u);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51700:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442))},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>u});var s=r(67989),i=r(19978),n=r(75535),o=r(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),u=(0,i.xI)(a),d=(0,n.aU)(a);(0,o.c7)(a)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>a});var s=r(37413),i=r(61421),n=r.n(i);r(82704);var o=r(94442);let a={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function u({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:n().className,children:(0,s.jsx)(o.AuthProvider,{children:e})})})}},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=[],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823],()=>r(77182));module.exports=s})();