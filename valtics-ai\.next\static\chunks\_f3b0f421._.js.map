{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YAEX,sCAAsC;YACtC,IAAI,CAAC,cAAc;gBACjB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,SAAS;oBACT,WAAW;gBACb,OAAO;oBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;oBACnF,MAAM,eAAe,oBAAoB,SAAS;oBAClD,SAAS;oBACT,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd;GApGwB;;QAKD,4HAAA,CAAA,eAAY;;;KALX", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/pricing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport ThemeToggle from '@/components/ThemeToggle';\n\nexport default function Pricing() {\n  const { user } = useAuth();\n\n  const plans = [\n    {\n      name: 'Basic',\n      price: 29,\n      period: 'month',\n      description: 'Perfect for small teams getting started with BVA',\n      features: [\n        'Up to 5 BVAs per month',\n        'Access to basic templates',\n        'Standard support',\n        'PDF exports',\n        'Basic analytics'\n      ],\n      buttonText: 'Start Basic Plan',\n      popular: false\n    },\n    {\n      name: 'Premium',\n      price: 79,\n      period: 'month',\n      description: 'Ideal for growing businesses with advanced needs',\n      features: [\n        'Unlimited BVAs',\n        'Access to all templates',\n        'Priority support',\n        'PDF & PowerPoint exports',\n        'Advanced analytics',\n        'Custom branding',\n        'API access'\n      ],\n      buttonText: 'Start Premium Plan',\n      popular: true\n    },\n    {\n      name: 'Enterprise',\n      price: 199,\n      period: 'month',\n      description: 'For large organizations with complex requirements',\n      features: [\n        'Everything in Premium',\n        'Custom templates',\n        'Dedicated account manager',\n        'SSO integration',\n        'Advanced security',\n        'Custom integrations',\n        'Training & onboarding'\n      ],\n      buttonText: 'Contact Sales',\n      popular: false\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Navigation */}\n      <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href={user ? \"/dashboard\" : \"/\"} className=\"flex items-center space-x-3\">\n                <Image\n                  src=\"/logo.png\"\n                  alt=\"VALTICS AI Logo\"\n                  width={32}\n                  height={32}\n                  className=\"w-8 h-8\"\n                />\n                <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                  VALTICS AI\n                </span>\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <Link\n                  href=\"/dashboard\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Dashboard\n                </Link>\n              ) : (\n                <>\n                  <Link\n                    href=\"/login\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Login\n                  </Link>\n                  <ThemeToggle />\n                  <Link\n                    href=\"/register\"\n                    className=\"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n                  >\n                    Sign Up\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-12 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Choose Your Plan\n            </h1>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n              Select the perfect plan for your business value analysis needs.\n              All plans include access to our AI-powered BVA platform.\n            </p>\n          </div>\n\n          {/* Pricing Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n            {plans.map((plan, index) => (\n              <div\n                key={plan.name}\n                className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden ${\n                  plan.popular ? 'ring-2 ring-blue-500 dark:ring-blue-400' : ''\n                }`}\n              >\n                {plan.popular && (\n                  <div className=\"absolute top-0 left-0 right-0 bg-blue-500 dark:bg-blue-600 text-white text-center py-2 text-sm font-medium\">\n                    Most Popular\n                  </div>\n                )}\n\n                <div className={`p-8 ${plan.popular ? 'pt-12' : ''}`}>\n                  <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">{plan.name}</h3>\n                  <p className=\"text-gray-600 dark:text-gray-300 mb-6\">{plan.description}</p>\n\n                  <div className=\"mb-6\">\n                    <span className=\"text-4xl font-bold text-gray-900 dark:text-white\">${plan.price}</span>\n                    <span className=\"text-gray-600 dark:text-gray-400\">/{plan.period}</span>\n                  </div>\n\n                  <ul className=\"space-y-3 mb-8\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-start\">\n                        <div className=\"flex-shrink-0 w-5 h-5 bg-green-500 dark:bg-green-600 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                          <span className=\"text-white text-xs\">✓</span>\n                        </div>\n                        <span className=\"text-gray-700 dark:text-gray-300\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  <button\n                    className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${\n                      plan.popular\n                        ? 'bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-800'\n                        : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600'\n                    }`}\n                  >\n                    {plan.buttonText}\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* FAQ Section */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center\">\n              Frequently Asked Questions\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Can I change my plan later?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Is there a free trial?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  We offer a 14-day free trial for all new users. No credit card required to get started.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  What payment methods do you accept?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  We accept all major credit cards, PayPal, and bank transfers for enterprise customers.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Can I cancel anytime?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Do you offer custom templates?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Custom templates are available for Enterprise customers. Contact our sales team for more information.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Is my data secure?\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Yes, we use enterprise-grade security measures including encryption, secure data centers, and regular security audits.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* CTA Section */}\n          <div className=\"text-center mt-16\">\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n              Ready to Get Started?\n            </h2>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8\">\n              Join thousands of businesses already using VALTICS AI to create compelling business value analyses.\n            </p>\n            <div className=\"flex justify-center space-x-4\">\n              {!user && (\n                <Link\n                  href=\"/register\"\n                  className=\"bg-blue-600 dark:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n                >\n                  Start Free Trial\n                </Link>\n              )}\n              <button className=\"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600\">\n                Contact Sales\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,OAAO,eAAe;oCAAK,WAAU;;sDAC/C,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAsD;;;;;;;;;;;;;;;;;0CAK1E,6LAAC;gCAAI,WAAU;0CACZ,qBACC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;yDAID;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,6HAAA,CAAA,UAAW;;;;;sDACZ,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,6LAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;sCAO5E,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAEC,WAAW,CAAC,wEAAwE,EAClF,KAAK,OAAO,GAAG,4CAA4C,IAC3D;;wCAED,KAAK,OAAO,kBACX,6LAAC;4CAAI,WAAU;sDAA6G;;;;;;sDAK9H,6LAAC;4CAAI,WAAW,CAAC,IAAI,EAAE,KAAK,OAAO,GAAG,UAAU,IAAI;;8DAClD,6LAAC;oDAAG,WAAU;8DAAyD,KAAK,IAAI;;;;;;8DAChF,6LAAC;oDAAE,WAAU;8DAAyC,KAAK,WAAW;;;;;;8DAEtE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAmD;gEAAE,KAAK,KAAK;;;;;;;sEAC/E,6LAAC;4DAAK,WAAU;;gEAAmC;gEAAE,KAAK,MAAM;;;;;;;;;;;;;8DAGlE,6LAAC;oDAAG,WAAU;8DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;4DAAsB,WAAU;;8EAC/B,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;8EAEvC,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;;2DAJ7C;;;;;;;;;;8DASb,6LAAC;oDACC,WAAW,CAAC,0DAA0D,EACpE,KAAK,OAAO,GACR,qFACA,uGACJ;8DAED,KAAK,UAAU;;;;;;;;;;;;;mCAtCf,KAAK,IAAI;;;;;;;;;;sCA8CpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAIlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAKlD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyD;;;;;;8DAGvE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;sCAQtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,sBACA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAIH,6LAAC;4CAAO,WAAU;sDAA+I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/K;GAjQwB;;QACL,2HAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}