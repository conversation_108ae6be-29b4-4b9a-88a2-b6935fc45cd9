(()=>{var e={};e.id=698,e.ids=[698],e.modules={533:(e,t,s)=>{Promise.resolve().then(s.bind(s,49441))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14101:(e,t,s)=>{Promise.resolve().then(s.bind(s,20435))},14985:e=>{"use strict";e.exports=require("dns")},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20435:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(43210),i=s(51108),n=s(16189),d=s(85814),l=s.n(d),c=s(75535),o=s(56304);function m(){let{user:e,loading:t,isAdmin:s}=(0,i.A)();(0,n.useRouter)();let[d,m]=(0,a.useState)([]),[x,u]=(0,a.useState)([]),[h,p]=(0,a.useState)([]),[v,g]=(0,a.useState)([]),[b,f]=(0,a.useState)(!0),[y,j]=(0,a.useState)("overview"),[w,N]=(0,a.useState)(!1),[A,P]=(0,a.useState)({name:"",description:"",logoUrl:""}),C=async()=>{try{f(!0);let e=(0,c.P)((0,c.rJ)(o.db,"brands"),(0,c.My)("name","asc")),t=(await (0,c.GG)(e)).docs.map(e=>({id:e.id,...e.data()}));m(t);let s=(0,c.P)((0,c.rJ)(o.db,"templates"),(0,c.My)("createdAt","desc")),r=(await (0,c.GG)(s)).docs.map(e=>({id:e.id,...e.data()}));u(r);let a=(0,c.P)((0,c.rJ)(o.db,"users"),(0,c.My)("createdAt","desc")),i=(await (0,c.GG)(a)).docs.map(e=>({id:e.id,...e.data()}));p(i);let n=(0,c.P)((0,c.rJ)(o.db,"bvaInstances"),(0,c.My)("createdAt","desc")),d=(await (0,c.GG)(n)).docs.map(e=>({id:e.id,...e.data()}));g(d)}catch(e){console.error("Error fetching admin data:",e)}finally{f(!1)}},k=async e=>{e.preventDefault();try{await (0,c.gS)((0,c.rJ)(o.db,"brands"),{...A,isActive:!0,createdAt:new Date}),P({name:"",description:"",logoUrl:""}),N(!1),C()}catch(e){console.error("Error creating brand:",e),alert("Error creating brand. Please try again.")}},S=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(o.db,"brands",e),{isActive:!t}),C()}catch(e){console.error("Error updating brand status:",e)}},D=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(o.db,"templates",e),{isActive:!t}),C()}catch(e){console.error("Error updating template status:",e)}};return t||b?(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})}):e&&s?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(l(),{href:"/dashboard",className:"text-xl font-semibold text-gray-900",children:"VALTICS AI - Admin"})}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)(l(),{href:"/dashboard",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"User Dashboard"})})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600",children:"Manage brands, templates, users, and system settings."})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",name:"Overview"},{id:"brands",name:"Brands"},{id:"templates",name:"Templates"},{id:"users",name:"Users"}].map(e=>(0,r.jsx)("button",{onClick:()=>j(e.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${y===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:e.name},e.id))})}),"overview"===y&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDC65"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Users"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:h.length})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83C\uDFE2"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Active Brands"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:d.filter(e=>e.isActive).length})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCC4"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Templates"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:x.length})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCCA"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"BVAs Created"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:v.length})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Users"}),(0,r.jsx)("div",{className:"space-y-3",children:h.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:e.role})]},e.id))})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent BVAs"}),(0,r.jsx)("div",{className:"space-y-3",children:v.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.clientName})]}),(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"in-progress"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:e.status})]},e.id))})]})})]})]}),"brands"===y&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Brands Management"}),(0,r.jsx)("button",{onClick:()=>N(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Add New Brand"})]}),w&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Brand"}),(0,r.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Brand Name *"}),(0,r.jsx)("input",{type:"text",value:A.name,onChange:e=>P({...A,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,r.jsx)("textarea",{value:A.description,onChange:e=>P({...A,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo URL"}),(0,r.jsx)("input",{type:"url",value:A.logoUrl,onChange:e=>P({...A,logoUrl:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{type:"submit",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Create Brand"}),(0,r.jsx)("button",{type:"button",onClick:()=>N(!1),className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400",children:"Cancel"})]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Templates"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[e.logoUrl&&(0,r.jsx)("img",{className:"h-8 w-8 rounded mr-3",src:e.logoUrl,alt:e.name}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x.filter(t=>t.brandId===e.id).length}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Active":"Inactive"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,r.jsx)("button",{onClick:()=>S(e.id,e.isActive),className:`mr-3 ${e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"}`,children:e.isActive?"Deactivate":"Activate"}),(0,r.jsx)(l(),{href:`/admin/brands/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:"Edit"})]})]},e.id))})]})})]}),"templates"===y&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Templates Management"}),(0,r.jsx)(l(),{href:"/admin/templates/new",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Upload New Template"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Template"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:x.map(e=>{let t=d.find(t=>t.id===e.brandId);return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:t?.name||"Unknown"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.category}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.price]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Active":"Inactive"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,r.jsx)("button",{onClick:()=>D(e.id,e.isActive),className:`mr-3 ${e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"}`,children:e.isActive?"Deactivate":"Activate"}),(0,r.jsx)(l(),{href:`/admin/templates/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:"Edit"})]})]},e.id)})})]})})]}),"users"===y&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Users management coming soon..."})})]})})]}):null}},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,s)=>{Promise.resolve().then(s.bind(s,94442)),Promise.resolve().then(s.bind(s,3465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,t,s)=>{"use strict";s.d(t,{D:()=>d,ThemeProvider:()=>n});var r=s(60687),a=s(43210);let i=(0,a.createContext)(null),n=({children:e})=>{let[t,s]=(0,a.useState)("light"),[n,d]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{let e,t=localStorage.getItem("valtics-theme");e=t||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");let r=document.documentElement;"dark"===e?r.classList.add("dark"):r.classList.remove("dark"),s(e),d(!0)},[]),(0,a.useEffect)(()=>{if(n){let e=document.documentElement;"dark"===t?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("valtics-theme",t)}},[t,n]),n)?(0,r.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")},setTheme:e=>{s(e)}},children:e}):(0,r.jsx)("div",{className:"min-h-screen bg-white",children:e})},d=()=>{let e=(0,a.useContext)(i);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e}},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49441:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\page.tsx","default")},51108:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,AuthProvider:()=>c});var r=s(60687),a=s(43210),i=s(19978),n=s(56304),d=s(75535);let l=(0,a.createContext)(null),c=({children:e})=>{let[t,s]=(0,a.useState)(null),[c,o]=(0,a.useState)(!0),[m,x]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=(0,i.hg)(n.j2,async e=>{if(s(e),e){let t=await (0,d.x7)((0,d.H9)(n.db,"users",e.uid));x(t.data()?.role==="admin")}else x(!1);o(!1)});return()=>e()},[]);let u=async(e,t)=>{await (0,i.x9)(n.j2,e,t)},h=async(e,t)=>{let s=await (0,i.eJ)(n.j2,e,t);await (0,d.BN)((0,d.H9)(n.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})},p=async()=>{await (0,i.CI)(n.j2)},v=async()=>{let e=new i.HF,t=await (0,i.df)(n.j2,e);(await (0,d.x7)((0,d.H9)(n.db,"users",t.user.uid))).exists()||await (0,d.BN)((0,d.H9)(n.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},g=async()=>{let e=new i.sk,t=await (0,i.df)(n.j2,e);(await (0,d.x7)((0,d.H9)(n.db,"users",t.user.uid))).exists()||await (0,d.BN)((0,d.H9)(n.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,r.jsx)(l.Provider,{value:{user:t,loading:c,signIn:u,signUp:h,logOut:p,signInWithGoogle:v,signInWithFacebook:g,isAdmin:m},children:e})},o=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>l});var r=s(67989),a=s(19978),i=s(75535),n=s(70146);let d=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,a.xI)(d),c=(0,i.aU)(d);(0,n.c7)(d)},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>l});var r=s(37413),a=s(61421),i=s.n(a);s(82704);var n=s(94442),d=s(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)(d.ThemeProvider,{children:(0,r.jsx)(n.AuthProvider,{children:e})})})})}},58497:(e,t,s)=>{Promise.resolve().then(s.bind(s,51108)),Promise.resolve().then(s.bind(s,40303))},61589:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")},98656:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(t,l);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49441)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,823,567],()=>s(98656));module.exports=r})();