'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { getTrialStatusMessage } from '@/lib/trial';

export default function TrialBanner() {
  const { user } = useAuth();
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't show banner if user is not a trial user, is admin, or banner is dismissed
  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {
    return null;
  }

  const { message, type, daysRemaining } = getTrialStatusMessage(user);

  // Don't show banner if no message
  if (!message) {
    return null;
  }

  const getBannerStyles = () => {
    switch (type) {
      case 'error':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';
      default:
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';
    }
  };

  const getButtonStyles = () => {
    switch (type) {
      case 'error':
        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';
      case 'warning':
        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';
      default:
        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';
    }
  };

  const getIconStyles = () => {
    switch (type) {
      case 'error':
        return 'text-red-400 dark:text-red-300';
      case 'warning':
        return 'text-yellow-400 dark:text-yellow-300';
      default:
        return 'text-blue-400 dark:text-blue-300';
    }
  };

  return (
    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            {type === 'error' ? (
              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            ) : type === 'warning' ? (
              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            )}
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium">
              {message}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <Link
            href="/pricing"
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}
          >
            Upgrade Now
          </Link>
          
          <button
            onClick={() => setIsDismissed(true)}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            aria-label="Dismiss banner"
          >
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

// Compact version for navigation bar
export function TrialIndicator() {
  const { user } = useAuth();

  if (!user || !user.isTrialUser || user.role === 'admin') {
    return null;
  }

  const { daysRemaining } = getTrialStatusMessage(user);

  if (daysRemaining <= 0) {
    return (
      <Link
        href="/pricing"
        className="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
      >
        Trial Expired
      </Link>
    );
  }

  const getIndicatorStyles = () => {
    if (daysRemaining <= 3) {
      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';
    }
    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';
  };

  return (
    <Link
      href="/pricing"
      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}
    >
      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left
    </Link>
  );
}
