'use client';

import { useState, useEffect } from 'react';

export default function ThemeTest() {
  const [isDark, setIsDark] = useState(false);

  const toggleTheme = () => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    
    const root = document.documentElement;
    if (newTheme) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  };

  return (
    <div className="p-4">
      <div className="bg-white dark:bg-slate-800 p-4 rounded border">
        <h2 className="text-gray-900 dark:text-white text-lg font-bold mb-2">
          Theme Test Component
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Current theme: {isDark ? 'Dark' : 'Light'}
        </p>
        <button
          onClick={toggleTheme}
          className="bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded hover:bg-blue-700 dark:hover:bg-blue-800"
        >
          Toggle Theme
        </button>
      </div>
    </div>
  );
}
