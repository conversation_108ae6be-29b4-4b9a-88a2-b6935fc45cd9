'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { BVAInstance, Template, Brand } from '@/types';

export default function BVADetail() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const bvaId = params.id as string;
  
  const [bva, setBva] = useState<BVAInstance | null>(null);
  const [template, setTemplate] = useState<Template | null>(null);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [loadingData, setLoadingData] = useState(true);
  const [generating, setGenerating] = useState(false);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user && bvaId) {
      fetchBVAData();
    }
  }, [user, bvaId]);

  const fetchBVAData = async () => {
    try {
      setLoadingData(true);

      // Fetch BVA instance
      const bvaDoc = await getDoc(doc(db, 'bvaInstances', bvaId));
      if (bvaDoc.exists()) {
        const bvaData = { id: bvaDoc.id, ...bvaDoc.data() } as BVAInstance;
        
        // Check if user owns this BVA
        if (bvaData.userId !== user?.uid) {
          router.push('/dashboard');
          return;
        }
        
        setBva(bvaData);

        // Fetch template if exists
        if (bvaData.templateId) {
          const templateDoc = await getDoc(doc(db, 'templates', bvaData.templateId));
          if (templateDoc.exists()) {
            const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;
            setTemplate(templateData);

            // Fetch brand
            const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));
            if (brandDoc.exists()) {
              setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);
            }
          }
        }
      } else {
        router.push('/dashboard');
      }

    } catch (error) {
      console.error('Error fetching BVA data:', error);
      router.push('/dashboard');
    } finally {
      setLoadingData(false);
    }
  };

  const generateReport = async () => {
    if (!bva) return;

    try {
      setGenerating(true);

      // Simulate report generation (in real app, this would call an AI service)
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Update BVA status to completed
      await updateDoc(doc(db, 'bvaInstances', bva.id), {
        status: 'completed',
        completedAt: new Date(),
        updatedAt: new Date()
      });

      // Refresh data
      await fetchBVAData();

    } catch (error) {
      console.error('Error generating report:', error);
      alert('Error generating report. Please try again.');
    } finally {
      setGenerating(false);
    }
  };

  const calculateROI = () => {
    if (!bva?.data) return 0;
    const benefits = bva.data.expectedBenefits || 0;
    const costs = bva.data.currentCosts || 0;
    if (costs === 0) return 0;
    return ((benefits - costs) / costs) * 100;
  };

  const calculatePaybackPeriod = () => {
    if (!bva?.data) return 0;
    const benefits = bva.data.expectedBenefits || 0;
    const costs = bva.data.currentCosts || 0;
    if (benefits === 0) return 0;
    return costs / (benefits / 12); // months
  };

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!user || !bva) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-xl font-semibold text-gray-900">
                VALTICS AI
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6">
              <div className="flex items-start justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {bva.name}
                  </h1>
                  <p className="text-lg text-gray-600 mb-4">
                    Client: {bva.clientName}
                  </p>
                  <div className="flex items-center space-x-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(bva.status)}`}>
                      {bva.status.charAt(0).toUpperCase() + bva.status.slice(1)}
                    </span>
                    {template && brand && (
                      <span className="text-sm text-gray-500">
                        Template: {template.name} by {brand.name}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex space-x-3">
                  {bva.status !== 'completed' && (
                    <button
                      onClick={generateReport}
                      disabled={generating}
                      className="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50"
                    >
                      {generating ? 'Generating...' : 'Generate Report'}
                    </button>
                  )}
                  <Link
                    href={`/bva/${bva.id}/edit`}
                    className="bg-gray-600 text-white px-6 py-2 rounded-md font-medium hover:bg-gray-700"
                  >
                    Edit
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-bold">ROI</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Return on Investment</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {calculateROI().toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-bold">$</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Annual Benefits</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ${(bva.data.expectedBenefits || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-bold">⏱</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Payback Period</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {calculatePaybackPeriod().toFixed(1)} mo
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-bold">📊</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Analysis Period</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {bva.data.timeframe || 12} mo
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Project Details */}
            <div className="bg-white rounded-lg shadow-md">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Project Details</h3>
                <dl className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Industry</dt>
                    <dd className="text-sm text-gray-900">{bva.data.industry || 'Not specified'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Current Annual Costs</dt>
                    <dd className="text-sm text-gray-900">${(bva.data.currentCosts || 0).toLocaleString()}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Expected Annual Benefits</dt>
                    <dd className="text-sm text-gray-900">${(bva.data.expectedBenefits || 0).toLocaleString()}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="text-sm text-gray-900">
                      {new Date(bva.createdAt).toLocaleDateString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="text-sm text-gray-900">
                      {new Date(bva.updatedAt).toLocaleDateString()}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Additional Information */}
            <div className="bg-white rounded-lg shadow-md">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                
                {bva.data.additionalData?.businessDrivers && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Business Drivers</h4>
                    <p className="text-sm text-gray-900">{bva.data.additionalData.businessDrivers}</p>
                  </div>
                )}

                {bva.data.additionalData?.successMetrics && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Success Metrics</h4>
                    <p className="text-sm text-gray-900">{bva.data.additionalData.successMetrics}</p>
                  </div>
                )}

                {bva.data.additionalData?.timeline && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Implementation Timeline</h4>
                    <p className="text-sm text-gray-900">{bva.data.additionalData.timeline}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Report Actions */}
          {bva.status === 'completed' && (
            <div className="mt-8 bg-white rounded-lg shadow-md">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Generated Reports</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Executive Summary</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Concise overview perfect for stakeholder presentations
                    </p>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-blue-700">
                      Download PDF
                    </button>
                  </div>
                  
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Full Report</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Detailed analysis with charts, tables, and calculations
                    </p>
                    <button className="bg-green-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-green-700">
                      Download PDF
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Chart Placeholder */}
          {bva.status === 'completed' && (
            <div className="mt-8 bg-white rounded-lg shadow-md">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Financial Analysis</h3>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-2">📊</div>
                    <p className="text-gray-500">Interactive charts and graphs would appear here</p>
                    <p className="text-sm text-gray-400 mt-1">ROI trends, cost-benefit analysis, payback timeline</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
