import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from './config';
import { Brand, Template, BVAInstance, User, Subscription } from '@/types';

// Brand Services
export const brandService = {
  async getAll(): Promise<Brand[]> {
    const q = query(collection(db, 'brands'), orderBy('name', 'asc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Brand));
  },

  async getActive(): Promise<Brand[]> {
    const q = query(
      collection(db, 'brands'),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Brand));
  },

  async getById(id: string): Promise<Brand | null> {
    const docRef = doc(db, 'brands', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as Brand : null;
  },

  async create(brand: Omit<Brand, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'brands'), {
      ...brand,
      createdAt: Timestamp.now()
    });
    return docRef.id;
  },

  async update(id: string, updates: Partial<Brand>): Promise<void> {
    const docRef = doc(db, 'brands', id);
    await updateDoc(docRef, updates);
  },

  async delete(id: string): Promise<void> {
    const docRef = doc(db, 'brands', id);
    await deleteDoc(docRef);
  }
};

// Template Services
export const templateService = {
  async getAll(): Promise<Template[]> {
    const q = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Template));
  },

  async getActive(): Promise<Template[]> {
    const q = query(
      collection(db, 'templates'),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Template));
  },

  async getByBrand(brandId: string): Promise<Template[]> {
    const q = query(
      collection(db, 'templates'),
      where('brandId', '==', brandId),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Template));
  },

  async getById(id: string): Promise<Template | null> {
    const docRef = doc(db, 'templates', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as Template : null;
  },

  async create(template: Omit<Template, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'templates'), {
      ...template,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    return docRef.id;
  },

  async update(id: string, updates: Partial<Template>): Promise<void> {
    const docRef = doc(db, 'templates', id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  },

  async delete(id: string): Promise<void> {
    const docRef = doc(db, 'templates', id);
    await deleteDoc(docRef);
  }
};

// BVA Instance Services
export const bvaService = {
  async getByUser(userId: string): Promise<BVAInstance[]> {
    const q = query(
      collection(db, 'bvaInstances'),
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BVAInstance));
  },

  async getById(id: string): Promise<BVAInstance | null> {
    const docRef = doc(db, 'bvaInstances', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as BVAInstance : null;
  },

  async create(bva: Omit<BVAInstance, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'bvaInstances'), {
      ...bva,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    return docRef.id;
  },

  async update(id: string, updates: Partial<BVAInstance>): Promise<void> {
    const docRef = doc(db, 'bvaInstances', id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  },

  async delete(id: string): Promise<void> {
    const docRef = doc(db, 'bvaInstances', id);
    await deleteDoc(docRef);
  },

  async getRecent(limitCount: number = 10): Promise<BVAInstance[]> {
    const q = query(
      collection(db, 'bvaInstances'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BVAInstance));
  }
};

// User Services
export const userService = {
  async getAll(): Promise<User[]> {
    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as User));
  },

  async getById(id: string): Promise<User | null> {
    const docRef = doc(db, 'users', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as User : null;
  },

  async create(userId: string, user: Omit<User, 'id'>): Promise<void> {
    const docRef = doc(db, 'users', userId);
    await updateDoc(docRef, {
      ...user,
      createdAt: Timestamp.now()
    });
  },

  async update(id: string, updates: Partial<User>): Promise<void> {
    const docRef = doc(db, 'users', id);
    await updateDoc(docRef, updates);
  }
};

// Subscription Services
export const subscriptionService = {
  async getByUser(userId: string): Promise<Subscription | null> {
    const q = query(
      collection(db, 'subscriptions'),
      where('userId', '==', userId)
    );
    const snapshot = await getDocs(q);
    if (snapshot.empty) return null;

    const doc = snapshot.docs[0];
    return { id: doc.id, ...doc.data() } as Subscription;
  },

  async create(subscription: Omit<Subscription, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'subscriptions'), subscription);
    return docRef.id;
  },

  async update(id: string, updates: Partial<Subscription>): Promise<void> {
    const docRef = doc(db, 'subscriptions', id);
    await updateDoc(docRef, updates);
  }
};

// File Upload Services
export const fileService = {
  async uploadFile(file: File, path: string): Promise<string> {
    const storageRef = ref(storage, path);
    const snapshot = await uploadBytes(storageRef, file);
    return await getDownloadURL(snapshot.ref);
  },

  async deleteFile(path: string): Promise<void> {
    const storageRef = ref(storage, path);
    await deleteObject(storageRef);
  },

  async uploadTemplateFile(templateId: string, file: File, fileType: 'pdf' | 'ppt' | 'excel'): Promise<string> {
    const path = `templates/${templateId}/${fileType}/${file.name}`;
    return await this.uploadFile(file, path);
  },

  async uploadBrandLogo(brandId: string, file: File): Promise<string> {
    const path = `brands/${brandId}/logo/${file.name}`;
    return await this.uploadFile(file, path);
  }
};

// Analytics Services
export const analyticsService = {
  async getDashboardStats(userId?: string) {
    const stats = {
      totalUsers: 0,
      totalBrands: 0,
      totalTemplates: 0,
      totalBVAs: 0,
      activeBrands: 0,
      activeTemplates: 0,
      completedBVAs: 0,
      userBVAs: 0
    };

    try {
      // Get total counts
      const [usersSnap, brandsSnap, templatesSnap, bvasSnap] = await Promise.all([
        getDocs(collection(db, 'users')),
        getDocs(collection(db, 'brands')),
        getDocs(collection(db, 'templates')),
        getDocs(collection(db, 'bvaInstances'))
      ]);

      stats.totalUsers = usersSnap.size;
      stats.totalBrands = brandsSnap.size;
      stats.totalTemplates = templatesSnap.size;
      stats.totalBVAs = bvasSnap.size;

      // Count active items
      stats.activeBrands = brandsSnap.docs.filter(doc => doc.data().isActive).length;
      stats.activeTemplates = templatesSnap.docs.filter(doc => doc.data().isActive).length;
      stats.completedBVAs = bvasSnap.docs.filter(doc => doc.data().status === 'completed').length;

      // User-specific stats
      if (userId) {
        stats.userBVAs = bvasSnap.docs.filter(doc => doc.data().userId === userId).length;
      }

      return stats;
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      return stats;
    }
  }
};
