{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YAEX,sCAAsC;YACtC,IAAI,CAAC,cAAc;gBACjB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,SAAS;oBACT,WAAW;gBACb,OAAO;oBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;oBACnF,MAAM,eAAe,oBAAoB,SAAS;oBAClD,SAAS;oBACT,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd;GApGwB;;QAKD,4HAAA,CAAA,eAAY;;;KALX", "debugId": null}}]}