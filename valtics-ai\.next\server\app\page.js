(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},4536:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11572:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90597)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27436:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),i=r(40303),a=r(43210);function o(){let{theme:e,toggleTheme:t}=(0,i.D)(),[r,o]=(0,a.useState)(!1);return r?(0,s.jsx)("button",{onClick:t,className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":`Switch to ${"light"===e?"dark":"light"} mode`,title:`Switch to ${"light"===e?"dark":"light"} mode`,children:"light"===e?(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})}):(0,s.jsx)("div",{className:"p-2 w-9 h-9"})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,ThemeProvider:()=>n});var s=r(60687),i=r(43210);let a=(0,i.createContext)(null),o=()=>{let e=(0,i.useContext)(a);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},n=({children:e})=>{let[t,r]=(0,i.useState)("light"),[o,n]=(0,i.useState)(!1);(0,i.useEffect)(()=>{n(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return o?(0,s.jsx)(a.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},46106:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\components\\\\ThemeToggle.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\components\\ThemeToggle.tsx","default")},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>d});var s=r(60687),i=r(43210),a=r(19978),o=r(56304),n=r(75535);let l=(0,i.createContext)(null),d=({children:e})=>{let[t,r]=(0,i.useState)(null),[d,c]=(0,i.useState)(!0),[u,h]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=(0,a.hg)(o.j2,async e=>{if(r(e),e){let t=await (0,n.x7)((0,n.H9)(o.db,"users",e.uid));h(t.data()?.role==="admin")}else h(!1);c(!1)});return()=>e()},[]);let m=async(e,t)=>{await (0,a.x9)(o.j2,e,t)},x=async(e,t)=>{let r=await (0,a.eJ)(o.j2,e,t);await (0,n.BN)((0,n.H9)(o.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},p=async()=>{await (0,a.CI)(o.j2)},v=async()=>{let e=new a.HF,t=await (0,a.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},g=async()=>{let e=new a.sk,t=await (0,a.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(l.Provider,{value:{user:t,loading:d,signIn:m,signUp:x,logOut:p,signInWithGoogle:v,signInWithFacebook:g,isAdmin:u},children:e})},c=()=>{let e=(0,i.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},55857:(e,t,r)=>{Promise.resolve().then(r.bind(r,27436)),Promise.resolve().then(r.t.bind(r,85814,23))},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>l});var s=r(67989),i=r(19978),a=r(75535),o=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,i.xI)(n),d=(0,a.aU)(n);(0,o.c7)(n)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),i=r(61421),a=r.n(i);r(82704);var o=r(94442),n=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(n.ThemeProvider,{children:(0,s.jsx)(o.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},90597:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413),i=r(4536),a=r.n(i),o=r(46106);function n(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:[(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"VALTICS AI"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(a(),{href:"/pricing",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Pricing"}),(0,s.jsx)(a(),{href:"/login",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Login"}),(0,s.jsx)(o.default,{}),(0,s.jsx)(a(),{href:"/register",className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Get Started"})]})]})})}),(0,s.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-5xl font-bold text-gray-900 dark:text-white mb-6",children:"Create Powerful Business Value Analysis Reports"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto",children:"Transform your technology solutions into compelling business cases with AI-powered templates and professional reporting tools."}),(0,s.jsxs)("div",{className:"flex justify-center gap-4 mb-16",children:[(0,s.jsx)(a(),{href:"/register",className:"bg-blue-600 dark:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Start Free Trial"}),(0,s.jsx)(a(),{href:"/pricing",className:"bg-white dark:bg-gray-700 text-gray-800 dark:text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600",children:"View Pricing"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-20",children:[(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("span",{className:"text-blue-600 dark:text-blue-400 text-2xl",children:"\uD83D\uDCCA"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Professional Templates"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Access pre-built BVA templates for Microsoft, security, and enterprise solutions."})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("span",{className:"text-green-600 dark:text-green-400 text-2xl",children:"\uD83E\uDD16"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"AI-Powered Analysis"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Generate executive summaries and detailed reports with intelligent automation."})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("span",{className:"text-purple-600 dark:text-purple-400 text-2xl",children:"\uD83D\uDCC8"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"ROI Calculations"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Automatic ROI, payback period, and benefit calculations with visual charts."})]})]})]})})]})}},90705:(e,t,r)=>{Promise.resolve().then(r.bind(r,46106)),Promise.resolve().then(r.t.bind(r,4536,23))},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,567],()=>r(11572));module.exports=s})();