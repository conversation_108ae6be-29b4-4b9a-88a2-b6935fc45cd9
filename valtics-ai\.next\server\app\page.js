(()=>{var e={};e.id=974,e.ids=[974],e.modules={597:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(7413),i=r(4536),a=r.n(i);function n(){return(0,s.jsx)("main",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,s.jsxs)("div",{className:"z-10 max-w-5xl w-full items-center justify-center font-mono text-sm",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-center mb-8",children:"VALTICS AI System"}),(0,s.jsx)("p",{className:"text-xl text-center mb-8",children:"Business Value Analysis Platform"}),(0,s.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,s.jsx)(a(),{href:"/login",className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Login"}),(0,s.jsx)(a(),{href:"/register",className:"px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300",children:"Register"})]})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1572:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var s=r(5239),i=r(8088),a=r(8170),n=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,597)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1700:(e,t,r)=>{Promise.resolve().then(r.bind(r,4442))},1820:e=>{"use strict";e.exports=require("os")},1861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},2264:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>h,A:()=>m});var s=r(687),i=r(3210),a=r(9978),n=r(7989),o=r(5535),l=r(146);let u=(0,n.Dk)().length?(0,n.Sx)():(0,n.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),d=(0,a.xI)(u),c=(0,o.aU)(u);(0,l.c7)(u);let p=(0,i.createContext)(null),h=({children:e})=>{let[t,r]=(0,i.useState)(null),[n,l]=(0,i.useState)(!0),[u,h]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=(0,a.hg)(d,async e=>{if(r(e),e){let t=await (0,o.x7)((0,o.H9)(c,"users",e.uid));h(t.data()?.role==="admin")}else h(!1);l(!1)});return()=>e()},[]);let m=async(e,t)=>{await (0,a.x9)(d,e,t)},v=async(e,t)=>{let r=await (0,a.eJ)(d,e,t);await (0,o.BN)((0,o.H9)(c,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},x=async()=>{await (0,a.CI)(d)},b=async()=>{let e=new a.HF,t=await (0,a.df)(d,e);(await (0,o.x7)((0,o.H9)(c,"users",t.user.uid))).exists()||await (0,o.BN)((0,o.H9)(c,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},f=async()=>{let e=new a.sk,t=await (0,a.df)(d,e);(await (0,o.x7)((0,o.H9)(c,"users",t.user.uid))).exists()||await (0,o.BN)((0,o.H9)(c,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(p.Provider,{value:{user:t,loading:n,signIn:m,signUp:v,logOut:x,signInWithGoogle:b,signInWithFacebook:f,isAdmin:u},children:e})},m=()=>{let e=(0,i.useContext)(p);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3496:e=>{"use strict";e.exports=require("http2")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4210:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},4442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(2907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},4536:(e,t,r)=>{let{createProxy:s}=r(9844);e.exports=s("C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4985:e=>{"use strict";e.exports=require("dns")},5511:e=>{"use strict";e.exports=require("crypto")},6055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},7732:(e,t,r)=>{Promise.resolve().then(r.bind(r,2264))},7910:e=>{"use strict";e.exports=require("stream")},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var s=r(7413),i=r(1421),a=r.n(i);r(2704);var n=r(4442);let o={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(n.AuthProvider,{children:e})})})}},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9771:e=>{"use strict";e.exports=require("process")},9930:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,658,814],()=>r(1572));module.exports=s})();