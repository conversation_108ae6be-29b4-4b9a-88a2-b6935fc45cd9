@import "tailwindcss";

/* Base styles */
body {
  font-family: Arial, Helvetica, sans-serif;
}

/* Ensure dark mode works properly */
html.dark {
  color-scheme: dark;
}

html {
  color-scheme: light;
}

/* Force dark mode styles for better compatibility */
html.dark body {
  background-color: rgb(15 23 42);
  color: rgb(248 250 252);
}

html.dark .bg-white {
  background-color: rgb(30 41 59) !important;
}

html.dark .bg-gray-50 {
  background-color: rgb(15 23 42) !important;
}

html.dark .text-gray-900 {
  color: rgb(248 250 252) !important;
}

html.dark .text-gray-700 {
  color: rgb(226 232 240) !important;
}

html.dark .text-gray-600 {
  color: rgb(203 213 225) !important;
}

html.dark .text-gray-500 {
  color: rgb(148 163 184) !important;
}

html.dark .border-gray-200 {
  border-color: rgb(71 85 105) !important;
}

html.dark .border-gray-300 {
  border-color: rgb(71 85 105) !important;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f8fafc;
}

::-webkit-scrollbar-thumb {
  background: #64748b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

/* Dark mode scrollbar */
html.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

html.dark ::-webkit-scrollbar-thumb {
  background: #94a3b8;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}
