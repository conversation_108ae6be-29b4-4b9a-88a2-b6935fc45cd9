@import "tailwindcss";

/* Base styles for light mode */
html {
  background-color: #ffffff;
  color: #171717;
}

body {
  font-family: Arial, Helvetica, sans-serif;
  background-color: inherit;
  color: inherit;
}

/* Dark mode styles */
html.dark {
  background-color: #0f172a;
  color: #f8fafc;
}

html.dark body {
  background-color: #0f172a;
  color: #f8fafc;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f8fafc;
}

::-webkit-scrollbar-thumb {
  background: #64748b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

/* Dark mode scrollbar */
html.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

html.dark ::-webkit-scrollbar-thumb {
  background: #94a3b8;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}
