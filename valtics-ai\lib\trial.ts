import { User } from '@/types';

// Trial duration in days
export const TRIAL_DURATION_DAYS = 10;

/**
 * Creates trial data for a new user
 */
export function createTrialData() {
  const now = new Date();
  const trialEndDate = new Date(now);
  trialEndDate.setDate(now.getDate() + TRIAL_DURATION_DAYS);

  return {
    isTrialUser: true,
    trialStartDate: now,
    trialEndDate: trialEndDate,
    trialExpired: false
  };
}

/**
 * Checks if a user's trial has expired
 */
export function isTrialExpired(user: User): boolean {
  if (!user.isTrialUser || !user.trialEndDate) {
    return false;
  }

  const now = new Date();
  const trialEnd = new Date(user.trialEndDate);
  return now > trialEnd;
}

/**
 * Gets the number of days remaining in trial
 */
export function getTrialDaysRemaining(user: User): number {
  if (!user.isTrialUser || !user.trialEndDate) {
    return 0;
  }

  const now = new Date();
  const trialEnd = new Date(user.trialEndDate);
  const diffTime = trialEnd.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
}

/**
 * Formats trial end date for display
 */
export function formatTrialEndDate(user: User): string {
  if (!user.trialEndDate) {
    return '';
  }

  return new Date(user.trialEndDate).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Checks if user can access premium features
 */
export function canAccessPremiumFeatures(user: User): boolean {
  // Admins always have access
  if (user.role === 'admin') {
    return true;
  }

  // Users with active subscriptions have access
  if (user.subscription && user.subscription.status === 'active') {
    return true;
  }

  // Trial users have access if trial hasn't expired
  if (user.isTrialUser && !isTrialExpired(user)) {
    return true;
  }

  return false;
}

/**
 * Gets trial status message for UI display
 */
export function getTrialStatusMessage(user: User): {
  message: string;
  type: 'info' | 'warning' | 'error';
  daysRemaining: number;
} {
  if (!user.isTrialUser) {
    return {
      message: '',
      type: 'info',
      daysRemaining: 0
    };
  }

  const daysRemaining = getTrialDaysRemaining(user);
  
  if (daysRemaining <= 0) {
    return {
      message: 'Your trial has expired. Upgrade to continue using premium features.',
      type: 'error',
      daysRemaining: 0
    };
  }

  if (daysRemaining <= 3) {
    return {
      message: `Your trial expires in ${daysRemaining} day${daysRemaining === 1 ? '' : 's'}. Upgrade now to continue.`,
      type: 'warning',
      daysRemaining
    };
  }

  return {
    message: `Your trial expires in ${daysRemaining} days on ${formatTrialEndDate(user)}.`,
    type: 'info',
    daysRemaining
  };
}

/**
 * Creates user data with trial information for new registrations
 */
export function createNewUserWithTrial(email: string, additionalData: Partial<User> = {}): Omit<User, 'id'> {
  const now = new Date();
  const trialData = createTrialData();

  return {
    email,
    role: 'user' as const,
    isActive: true,
    createdAt: now,
    ...trialData,
    ...additionalData
  };
}
