(()=>{var e={};e.id=454,e.ids=[454],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1700:(e,t,r)=>{Promise.resolve().then(r.bind(r,4442))},1820:e=>{"use strict";e.exports=require("os")},1861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},1993:(e,t,r)=>{Promise.resolve().then(r.bind(r,8874))},2264:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>m,A:()=>v});var s=r(687),i=r(3210),o=r(9978),a=r(7989),n=r(5535),l=r(146);let u=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),d=(0,o.xI)(u),p=(0,n.aU)(u);(0,l.c7)(u);let c=(0,i.createContext)(null),m=({children:e})=>{let[t,r]=(0,i.useState)(null),[a,l]=(0,i.useState)(!0),[u,m]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=(0,o.hg)(d,async e=>{if(r(e),e){let t=await (0,n.x7)((0,n.H9)(p,"users",e.uid));m(t.data()?.role==="admin")}else m(!1);l(!1)});return()=>e()},[]);let v=async(e,t)=>{await (0,o.x9)(d,e,t)},h=async(e,t)=>{let r=await (0,o.eJ)(d,e,t);await (0,n.BN)((0,n.H9)(p,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},x=async()=>{await (0,o.CI)(d)},b=async()=>{let e=new o.HF,t=await (0,o.df)(d,e);(await (0,n.x7)((0,n.H9)(p,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(p,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},f=async()=>{let e=new o.sk,t=await (0,o.df)(d,e);(await (0,n.x7)((0,n.H9)(p,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(p,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(c.Provider,{value:{user:t,loading:a,signIn:v,signUp:h,logOut:x,signInWithGoogle:b,signInWithFacebook:f,isAdmin:u},children:e})},v=()=>{let e=(0,i.useContext)(c);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},2265:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,7319,23))},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3496:e=>{"use strict";e.exports=require("http2")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(2907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4985:e=>{"use strict";e.exports=require("dns")},5511:e=>{"use strict";e.exports=require("crypto")},6050:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=r(5239),i=r(8088),o=r(8170),a=r.n(o),n=r(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let u={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8874)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},7319:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m You are attempting to export \"metadata\" from a component marked with \"use client\", which is disallowed. Either remove the export, or the \"use client\" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client\n  \x1b[31m|\x1b[0m\n\n    ,-[\x1b[36;1;4mC:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx\x1b[0m:25:1]\n \x1b[2m22\x1b[0m | \n \x1b[2m23\x1b[0m | const inter = Inter({ subsets: ['latin'] });\n \x1b[2m24\x1b[0m | \n \x1b[2m25\x1b[0m | export const metadata: Metadata = {\n    : \x1b[35;1m             ^^^^^^^^\x1b[0m\n \x1b[2m26\x1b[0m |   title: 'VALTICS AI System',\n \x1b[2m27\x1b[0m |   description: 'Business Value Analysis Platform',\n \x1b[2m28\x1b[0m | };\n    `----\n")},7732:(e,t,r)=>{Promise.resolve().then(r.bind(r,2264))},7910:e=>{"use strict";e.exports=require("stream")},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n});var s=r(7413),i=r(1421),o=r.n(i);r(2704);var a=r(4442);let n={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:o().className,children:(0,s.jsx)(a.AuthProvider,{children:e})})})}},8354:e=>{"use strict";e.exports=require("util")},8874:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AuthProvider:()=>a,default:()=>o,metadata:()=>i,useAuth:()=>n});var s=r(2907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call metadata() from the server but metadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx","metadata"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx","default"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx","AuthProvider"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx","useAuth")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9771:e=>{"use strict";e.exports=require("process")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,658],()=>r(6050));module.exports=s})();