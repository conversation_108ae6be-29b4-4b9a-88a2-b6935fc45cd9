(()=>{var e={};e.id=454,e.ids=[454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32265:(e,t,r)=>{Promise.resolve().then(r.bind(r,97319))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o,ThemeProvider:()=>n});var s=r(60687),i=r(43210);let a=(0,i.createContext)(null),o=()=>(0,i.useContext)(a),n=({children:e})=>{let[t,r]=(0,i.useState)("light"),[o,n]=(0,i.useState)(!1);(0,i.useEffect)(()=>{n(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return o?(0,s.jsx)(a.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},41993:(e,t,r)=>{Promise.resolve().then(r.bind(r,66493))},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,AuthProvider:()=>d});var s=r(60687),i=r(43210),a=r(19978),o=r(56304),n=r(75535);let l=(0,i.createContext)(null),d=({children:e})=>{let[t,r]=(0,i.useState)(null),[d,u]=(0,i.useState)(!0),[c,m]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=(0,a.hg)(o.j2,async e=>{if(r(e),e){let t=await (0,n.x7)((0,n.H9)(o.db,"users",e.uid));m(t.data()?.role==="admin")}else m(!1);u(!1)});return()=>e()},[]);let h=async(e,t)=>{await (0,a.x9)(o.j2,e,t)},p=async(e,t)=>{let r=await (0,a.eJ)(o.j2,e,t);await (0,n.BN)((0,n.H9)(o.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},v=async()=>{await (0,a.CI)(o.j2)},x=async()=>{let e=new a.HF,t=await (0,a.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},f=async()=>{let e=new a.sk,t=await (0,a.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(l.Provider,{value:{user:t,loading:d,signIn:h,signUp:p,logOut:v,signInWithGoogle:x,signInWithFacebook:f,isAdmin:c},children:e})},u=()=>{let e=(0,i.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>l});var s=r(67989),i=r(19978),a=r(75535),o=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,i.xI)(n),d=(0,a.aU)(n);(0,o.c7)(n)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),i=r(61421),a=r.n(i);r(82704);var o=r(94442),n=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(n.ThemeProvider,{children:(0,s.jsx)(o.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx","default")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")},96050:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66493)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},97319:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),a=r(51108),o=r(16189),n=r(85814),l=r.n(n);function d(){let[e,t]=(0,i.useState)(""),[r,n]=(0,i.useState)(""),[d,u]=(0,i.useState)(""),[c,m]=(0,i.useState)(""),{signUp:h,signInWithGoogle:p,signInWithFacebook:v}=(0,a.A)(),x=(0,o.useRouter)(),f=async t=>{if(t.preventDefault(),m(""),r!==d)return void m("Passwords do not match");try{await h(e,r),x.push("/dashboard")}catch(e){m(e.message)}},b=async()=>{try{await p(),x.push("/dashboard")}catch(e){m(e.message)}},g=async()=>{try{await v(),x.push("/dashboard")}catch(e){m(e.message)}};return(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Register for VALTICS AI"})}),c&&(0,s.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:c}),(0,s.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:r,onChange:e=>n(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:d,onChange:e=>u(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Register"})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsx)("button",{onClick:b,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Google"}),(0,s.jsx)("button",{onClick:g,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Facebook"})]})]}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Login"})]})})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,567],()=>r(96050));module.exports=s})();