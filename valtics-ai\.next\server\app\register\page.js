(()=>{var e={};e.id=454,e.ids=[454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>a});var s=t(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,r,t)=>{Promise.resolve().then(t.bind(t,94442)),Promise.resolve().then(t.bind(t,3465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32265:(e,r,t)=>{Promise.resolve().then(t.bind(t,97319))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,r,t)=>{"use strict";t.d(r,{Q:()=>o,ThemeProvider:()=>n});var s=t(60687),a=t(43210);let i=(0,a.createContext)(null),o=()=>(0,a.useContext)(i),n=({children:e})=>{let[r,t]=(0,a.useState)("light"),[o,n]=(0,a.useState)(!1);(0,a.useEffect)(()=>{n(!0);let e=localStorage.getItem("theme");if(e)t(e),d(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";t(e),d(e)}},[]);let d=e=>{let r=document.documentElement;"dark"===e?r.classList.add("dark"):r.classList.remove("dark")};return o?(0,s.jsx)(i.Provider,{value:{theme:r,toggleTheme:()=>{let e="light"===r?"dark":"light";t(e),d(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},41993:(e,r,t)=>{Promise.resolve().then(t.bind(t,66493))},46055:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,r,t)=>{"use strict";t.d(r,{A:()=>u,AuthProvider:()=>l});var s=t(60687),a=t(43210),i=t(19978),o=t(56304),n=t(75535);let d=(0,a.createContext)(null),l=({children:e})=>{let[r,t]=(0,a.useState)(null),[l,u]=(0,a.useState)(!0),[c,m]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=(0,i.hg)(o.j2,async e=>{if(t(e),e){let r=await (0,n.x7)((0,n.H9)(o.db,"users",e.uid));m(r.data()?.role==="admin")}else m(!1);u(!1)});return()=>e()},[]);let h=async(e,r)=>{await (0,i.x9)(o.j2,e,r)},p=async(e,r)=>{let t=await (0,i.eJ)(o.j2,e,r);await (0,n.BN)((0,n.H9)(o.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},x=async()=>{await (0,i.CI)(o.j2)},v=async()=>{let e=new i.HF,r=await (0,i.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",r.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},b=async()=>{let e=new i.sk,r=await (0,i.df)(o.j2,e);(await (0,n.x7)((0,n.H9)(o.db,"users",r.user.uid))).exists()||await (0,n.BN)((0,n.H9)(o.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(d.Provider,{value:{user:r,loading:l,signIn:h,signUp:p,logOut:x,signInWithGoogle:v,signInWithFacebook:b,isAdmin:c},children:e})},u=()=>{let e=(0,a.useContext)(d);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,r,t)=>{"use strict";t.d(r,{db:()=>l,j2:()=>d});var s=t(67989),a=t(19978),i=t(75535),o=t(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),d=(0,a.xI)(n),l=(0,i.aU)(n);(0,o.c7)(n)},58014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>d});var s=t(37413),a=t(61421),i=t.n(a);t(82704);var o=t(94442),n=t(3465);let d={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:i().className,children:(0,s.jsx)(n.ThemeProvider,{children:(0,s.jsx)(o.AuthProvider,{children:e})})})})}},58497:(e,r,t)=>{Promise.resolve().then(t.bind(t,51108)),Promise.resolve().then(t.bind(t,40303))},61589:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66493:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx","default")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>a});var s=t(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")},96050:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66493)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\register\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},97319:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),a=t(43210),i=t(51108),o=t(16189),n=t(85814),d=t.n(n);function l(){let[e,r]=(0,a.useState)(""),[t,n]=(0,a.useState)(""),[l,u]=(0,a.useState)(""),[c,m]=(0,a.useState)(""),{signUp:h,signInWithGoogle:p,signInWithFacebook:x}=(0,i.A)(),v=(0,o.useRouter)(),b=async r=>{if(r.preventDefault(),m(""),t!==l)return void m("Passwords do not match");try{await h(e,t),v.push("/dashboard")}catch(e){m(e.message)}},g=async()=>{try{await p(),v.push("/dashboard")}catch(e){m(e.message)}},f=async()=>{try{await x(),v.push("/dashboard")}catch(e){m(e.message)}};return(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24 bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Register for VALTICS AI"})}),c&&(0,s.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:c}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:t,onChange:e=>n(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:l,onChange:e=>u(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Register"})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400",children:"Or continue with"})})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsx)("button",{onClick:g,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Google"}),(0,s.jsx)("button",{onClick:f,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Facebook"})]})]}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Already have an account?"," ",(0,s.jsx)(d(),{href:"/login",className:"font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300",children:"Login"})]})})]})})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,823,567],()=>t(96050));module.exports=s})();