(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{1138:(e,s,t)=>{"use strict";t.d(s,{db:()=>c,j2:()=>n});var a=t(3915),r=t(6203),d=t(5317),l=t(858);let i=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),n=(0,r.xI)(i),c=(0,d.aU)(i);(0,l.c7)(i)},3274:(e,s,t)=>{"use strict";t.d(s,{A:()=>m,AuthProvider:()=>c});var a=t(5155),r=t(2115),d=t(6203),l=t(1138),i=t(5317);let n=(0,r.createContext)(null),c=e=>{let{children:s}=e,[t,c]=(0,r.useState)(null),[m,x]=(0,r.useState)(!0),[u,o]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=(0,d.hg)(l.j2,async e=>{if(c(e),e){var s;o((null==(s=(await (0,i.x7)((0,i.H9)(l.db,"users",e.uid))).data())?void 0:s.role)==="admin")}else o(!1);x(!1)});return()=>e()},[]);let h=async(e,s)=>{await (0,d.x9)(l.j2,e,s)},b=async(e,s)=>{let t=await (0,d.eJ)(l.j2,e,s);await (0,i.BN)((0,i.H9)(l.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},g=async()=>{await (0,d.CI)(l.j2)},p=async()=>{let e=new d.HF,s=await (0,d.df)(l.j2,e);(await (0,i.x7)((0,i.H9)(l.db,"users",s.user.uid))).exists()||await (0,i.BN)((0,i.H9)(l.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})},y=async()=>{let e=new d.sk,s=await (0,d.df)(l.j2,e);(await (0,i.x7)((0,i.H9)(l.db,"users",s.user.uid))).exists()||await (0,i.BN)((0,i.H9)(l.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})};return(0,a.jsx)(n.Provider,{value:{user:t,loading:m,signIn:h,signUp:b,logOut:g,signInWithGoogle:p,signInWithFacebook:y,isAdmin:u},children:s})},m=()=>{let e=(0,r.useContext)(n);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},3581:(e,s,t)=>{Promise.resolve().then(t.bind(t,8591))},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},8591:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),r=t(2115),d=t(3274),l=t(5695),i=t(6874),n=t.n(i),c=t(5317),m=t(1138);function x(){let{user:e,loading:s,logOut:t}=(0,d.A)(),i=(0,l.useRouter)(),[x,u]=(0,r.useState)(null),[o,h]=(0,r.useState)(null),[b,g]=(0,r.useState)([]),[p,y]=(0,r.useState)(!0),[j,N]=(0,r.useState)("profile"),[f,v]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s||e||i.push("/login")},[e,s,i]),(0,r.useEffect)(()=>{e&&w()},[e]);let w=async()=>{try{y(!0);let s=await (0,c.x7)((0,c.H9)(m.db,"users",e.uid));s.exists()&&u({id:s.id,...s.data()});let t=(0,c.P)((0,c.rJ)(m.db,"subscriptions"),(0,c._M)("userId","==",e.uid)),a=await (0,c.GG)(t);if(!a.empty){let e=a.docs[0];h({id:e.id,...e.data()})}let r=(0,c.P)((0,c.rJ)(m.db,"bvaInstances"),(0,c._M)("userId","==",e.uid),(0,c.My)("updatedAt","desc")),d=(await (0,c.GG)(r)).docs.map(e=>({id:e.id,...e.data()}));g(d)}catch(e){console.error("Error fetching profile data:",e)}finally{y(!1)}},S=async()=>{try{await t(),i.push("/")}catch(e){console.error("Error logging out:",e)}};if(s||p)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})});if(!e||!x)return null;let A=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"in-progress":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(n(),{href:"/dashboard",className:"text-xl font-semibold text-gray-900",children:"VALTICS AI"})}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsx)(n(),{href:"/dashboard",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"← Back to Dashboard"})})]})})}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Profile & Settings"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Manage your account settings and view your activity."})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"profile",name:"Profile"},{id:"subscription",name:"Subscription"},{id:"bvas",name:"My BVAs"}].map(e=>(0,a.jsx)("button",{onClick:()=>N(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(j===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.name},e.id))})}),"profile"===j&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Account Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:e.email||"",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Email cannot be changed"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Type"}),(0,a.jsx)("input",{type:"text",value:"admin"===x.role?"Administrator":"Standard User",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member Since"}),(0,a.jsx)("input",{type:"text",value:new Date(x.createdAt).toLocaleDateString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Total BVAs Created"}),(0,a.jsx)("input",{type:"text",value:b.length.toString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"})]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Account Actions"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Change Password"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Update your account password"})]}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"Change Password"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Download Data"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Export all your BVA data"})]}),(0,a.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700",children:"Export Data"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-red-900",children:"Sign Out"}),(0,a.jsx)("p",{className:"text-sm text-red-600",children:"Sign out of your account"})]}),(0,a.jsx)("button",{onClick:S,className:"bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700",children:"Sign Out"})]})]})]})})]}),"subscription"===j&&(0,a.jsx)("div",{className:"space-y-6",children:o?(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Current Subscription"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan Type"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("enterprise"===o.type?"bg-purple-100 text-purple-800":"premium"===o.type?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:o.type.charAt(0).toUpperCase()+o.type.slice(1)})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("active"===o.status?"bg-green-100 text-green-800":"cancelled"===o.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:o.status.charAt(0).toUpperCase()+o.status.slice(1)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:new Date(o.startDate).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:new Date(o.endDate).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Template Access"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:[o.accessibleTemplateIds.length," templates available"]})]})]}),(0,a.jsxs)("div",{className:"mt-6 flex space-x-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"Upgrade Plan"}),(0,a.jsx)("button",{className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400",children:"Manage Billing"})]})]})}):(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCB3"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Active Subscription"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Subscribe to access premium templates and features."}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700",children:"View Subscription Plans"})]})})}),"bvas"===j&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Your BVAs"}),b.length>0?(0,a.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Client: ",e.clientName]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:["Last updated: ",new Date(e.updatedAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(A(e.status)),children:e.status}),(0,a.jsx)(n(),{href:"/bva/".concat(e.id),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View"})]})]})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\uD83D\uDCCA"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No BVAs created yet."}),(0,a.jsx)(n(),{href:"/bva/new",className:"mt-4 inline-block bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"Create Your First BVA"})]})]})})})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,288,874,441,684,358],()=>s(3581)),_N_E=e.O()}]);