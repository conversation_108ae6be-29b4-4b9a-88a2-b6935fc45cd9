'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Brand, Template } from '@/types';

export default function Brands() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [templates, setTemplates] = useState<Record<string, Template[]>>({});
  const [loadingData, setLoadingData] = useState(true);
  const [selectedBrand, setSelectedBrand] = useState<string | null>(null);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user) {
      fetchBrandsData();
    }
  }, [user]);

  const fetchBrandsData = async () => {
    try {
      setLoadingData(true);

      // Fetch active brands
      const brandsQuery = query(
        collection(db, 'brands'),
        where('isActive', '==', true),
        orderBy('name', 'asc')
      );
      const brandsSnapshot = await getDocs(brandsQuery);
      const brandsData = brandsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Brand[];
      setBrands(brandsData);

      // Fetch templates for each brand
      const templatesData: Record<string, Template[]> = {};
      for (const brand of brandsData) {
        const templatesQuery = query(
          collection(db, 'templates'),
          where('brandId', '==', brand.id),
          where('isActive', '==', true),
          orderBy('name', 'asc')
        );
        const templatesSnapshot = await getDocs(templatesQuery);
        templatesData[brand.id] = templatesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Template[];
      }
      setTemplates(templatesData);

    } catch (error) {
      console.error('Error fetching brands data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-xl font-semibold text-gray-900">
                VALTICS AI
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Dashboard
              </Link>
              <Link
                href="/templates"
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Templates
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Solution Brands</h1>
            <p className="mt-2 text-gray-600">
              Explore our comprehensive collection of technology solution brands and their BVA templates.
            </p>
          </div>

          {brands.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {brands.map((brand) => (
                <div key={brand.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    {brand.logoUrl && (
                      <div className="mb-4">
                        <img
                          src={brand.logoUrl}
                          alt={`${brand.name} logo`}
                          className="h-12 w-auto"
                        />
                      </div>
                    )}
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {brand.name}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {brand.description}
                    </p>
                    
                    {/* Templates count */}
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-gray-500">
                        {templates[brand.id]?.length || 0} templates available
                      </span>
                      <button
                        onClick={() => setSelectedBrand(selectedBrand === brand.id ? null : brand.id)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        {selectedBrand === brand.id ? 'Hide Templates' : 'View Templates'}
                      </button>
                    </div>

                    {/* Templates list (collapsible) */}
                    {selectedBrand === brand.id && templates[brand.id] && (
                      <div className="border-t pt-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">Available Templates:</h4>
                        <div className="space-y-2">
                          {templates[brand.id].map((template) => (
                            <div key={template.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <div>
                                <p className="text-sm font-medium text-gray-900">{template.name}</p>
                                <p className="text-xs text-gray-500">{template.category}</p>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-green-600">${template.price}</span>
                                <Link
                                  href={`/templates/${template.id}`}
                                  className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-blue-700"
                                >
                                  Use
                                </Link>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* View all templates button */}
                    <div className="mt-4">
                      <Link
                        href={`/templates?brand=${brand.id}`}
                        className="w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 inline-block text-center"
                      >
                        Explore {brand.name} Templates
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📦</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No brands available</h3>
              <p className="text-gray-600">
                Check back later for new solution brands and templates.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
