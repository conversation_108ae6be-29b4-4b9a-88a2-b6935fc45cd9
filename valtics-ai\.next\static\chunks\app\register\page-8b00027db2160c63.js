(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{1851:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var a=r(5155),t=r(2115),l=r(9481),d=r(5695),i=r(6874),o=r.n(i);function n(){let[e,s]=(0,t.useState)(""),[r,i]=(0,t.useState)(""),[n,u]=(0,t.useState)(""),[c,m]=(0,t.useState)(""),{signUp:x,signInWithGoogle:h,signInWithFacebook:b}=(0,l.A)(),f=(0,d.useRouter)(),p=async s=>{if(s.preventDefault(),m(""),r!==n)return void m("Passwords do not match");try{await x(e,r),f.push("/dashboard")}catch(e){m(e.message)}},w=async()=>{try{await h(),f.push("/dashboard")}catch(e){m(e.message)}},g=async()=>{try{await b(),f.push("/dashboard")}catch(e){m(e.message)}};return(0,a.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,a.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Register for VALTICS AI"})}),c&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:c}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:r,onChange:e=>i(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,a.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:n,onChange:e=>u(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Register"})})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,a.jsx)("button",{onClick:w,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Google"}),(0,a.jsx)("button",{onClick:g,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Facebook"})]})]}),(0,a.jsx)("div",{className:"text-center mt-4",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)(o(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Login"})]})})]})})}},5695:(e,s,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}})},8519:(e,s,r)=>{Promise.resolve().then(r.bind(r,1851))},9481:(e,s,r)=>{"use strict";r.d(s,{AuthProvider:()=>x,A:()=>h});var a=r(5155),t=r(2115),l=r(6203),d=r(3915),i=r(5317),o=r(858);let n=(0,d.Dk)().length?(0,d.Sx)():(0,d.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),u=(0,l.xI)(n),c=(0,i.aU)(n);(0,o.c7)(n);let m=(0,t.createContext)(null),x=e=>{let{children:s}=e,[r,d]=(0,t.useState)(null),[o,n]=(0,t.useState)(!0),[x,h]=(0,t.useState)(!1);(0,t.useEffect)(()=>{let e=(0,l.hg)(u,async e=>{if(d(e),e){var s;h((null==(s=(await (0,i.x7)((0,i.H9)(c,"users",e.uid))).data())?void 0:s.role)==="admin")}else h(!1);n(!1)});return()=>e()},[]);let b=async(e,s)=>{await (0,l.x9)(u,e,s)},f=async(e,s)=>{let r=await (0,l.eJ)(u,e,s);await (0,i.BN)((0,i.H9)(c,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},p=async()=>{await (0,l.CI)(u)},w=async()=>{let e=new l.HF,s=await (0,l.df)(u,e);(await (0,i.x7)((0,i.H9)(c,"users",s.user.uid))).exists()||await (0,i.BN)((0,i.H9)(c,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})},g=async()=>{let e=new l.sk,s=await (0,l.df)(u,e);(await (0,i.x7)((0,i.H9)(c,"users",s.user.uid))).exists()||await (0,i.BN)((0,i.H9)(c,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})};return(0,a.jsx)(m.Provider,{value:{user:r,loading:o,signIn:b,signUp:f,logOut:p,signInWithGoogle:w,signInWithFacebook:g,isAdmin:x},children:s})},h=()=>{let e=(0,t.useContext)(m);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,288,874,441,684,358],()=>s(8519)),_N_E=e.O()}]);