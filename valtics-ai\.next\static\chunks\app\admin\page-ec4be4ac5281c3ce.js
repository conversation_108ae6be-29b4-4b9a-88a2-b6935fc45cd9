(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{1138:(e,s,t)=>{"use strict";t.d(s,{db:()=>n,j2:()=>c});var a=t(3915),r=t(6203),d=t(5317),i=t(858);let l=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),c=(0,r.xI)(l),n=(0,d.aU)(l);(0,i.c7)(l)},3274:(e,s,t)=>{"use strict";t.d(s,{A:()=>x,AuthProvider:()=>n});var a=t(5155),r=t(2115),d=t(6203),i=t(1138),l=t(5317);let c=(0,r.createContext)(null),n=e=>{let{children:s}=e,[t,n]=(0,r.useState)(null),[x,m]=(0,r.useState)(!0),[o,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=(0,d.hg)(i.j2,async e=>{if(n(e),e){var s;h((null==(s=(await (0,l.x7)((0,l.H9)(i.db,"users",e.uid))).data())?void 0:s.role)==="admin")}else h(!1);m(!1)});return()=>e()},[]);let u=async(e,s)=>{await (0,d.x9)(i.j2,e,s)},p=async(e,s)=>{let t=await (0,d.eJ)(i.j2,e,s);await (0,l.BN)((0,l.H9)(i.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},g=async()=>{await (0,d.CI)(i.j2)},j=async()=>{let e=new d.HF,s=await (0,d.df)(i.j2,e);(await (0,l.x7)((0,l.H9)(i.db,"users",s.user.uid))).exists()||await (0,l.BN)((0,l.H9)(i.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})},b=async()=>{let e=new d.sk,s=await (0,d.df)(i.j2,e);(await (0,l.x7)((0,l.H9)(i.db,"users",s.user.uid))).exists()||await (0,l.BN)((0,l.H9)(i.db,"users",s.user.uid),{email:s.user.email,role:"user",createdAt:new Date})};return(0,a.jsx)(c.Provider,{value:{user:t,loading:x,signIn:u,signUp:p,logOut:g,signInWithGoogle:j,signInWithFacebook:b,isAdmin:o},children:s})},x=()=>{let e=(0,r.useContext)(c);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},7161:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5155),r=t(2115),d=t(3274),i=t(5695),l=t(6874),c=t.n(l),n=t(5317),x=t(1138);function m(){let{user:e,loading:s,isAdmin:t}=(0,d.A)(),l=(0,i.useRouter)(),[m,o]=(0,r.useState)([]),[h,u]=(0,r.useState)([]),[p,g]=(0,r.useState)([]),[j,b]=(0,r.useState)([]),[y,f]=(0,r.useState)(!0),[N,v]=(0,r.useState)("overview"),[w,A]=(0,r.useState)(!1),[k,D]=(0,r.useState)({name:"",description:"",logoUrl:""});(0,r.useEffect)(()=>{s||e&&t||l.push("/dashboard")},[e,s,t,l]),(0,r.useEffect)(()=>{e&&t&&C()},[e,t]);let C=async()=>{try{f(!0);let e=(0,n.P)((0,n.rJ)(x.db,"brands"),(0,n.My)("name","asc")),s=(await (0,n.GG)(e)).docs.map(e=>({id:e.id,...e.data()}));o(s);let t=(0,n.P)((0,n.rJ)(x.db,"templates"),(0,n.My)("createdAt","desc")),a=(await (0,n.GG)(t)).docs.map(e=>({id:e.id,...e.data()}));u(a);let r=(0,n.P)((0,n.rJ)(x.db,"users"),(0,n.My)("createdAt","desc")),d=(await (0,n.GG)(r)).docs.map(e=>({id:e.id,...e.data()}));g(d);let i=(0,n.P)((0,n.rJ)(x.db,"bvaInstances"),(0,n.My)("createdAt","desc")),l=(await (0,n.GG)(i)).docs.map(e=>({id:e.id,...e.data()}));b(l)}catch(e){console.error("Error fetching admin data:",e)}finally{f(!1)}},S=async e=>{e.preventDefault();try{await (0,n.gS)((0,n.rJ)(x.db,"brands"),{...k,isActive:!0,createdAt:new Date}),D({name:"",description:"",logoUrl:""}),A(!1),C()}catch(e){console.error("Error creating brand:",e),alert("Error creating brand. Please try again.")}},E=async(e,s)=>{try{await (0,n.mZ)((0,n.H9)(x.db,"brands",e),{isActive:!s}),C()}catch(e){console.error("Error updating brand status:",e)}},P=async(e,s)=>{try{await (0,n.mZ)((0,n.H9)(x.db,"templates",e),{isActive:!s}),C()}catch(e){console.error("Error updating template status:",e)}};return s||y?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})}):e&&t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(c(),{href:"/dashboard",className:"text-xl font-semibold text-gray-900",children:"VALTICS AI - Admin"})}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsx)(c(),{href:"/dashboard",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"User Dashboard"})})]})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Manage brands, templates, users, and system settings."})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",name:"Overview"},{id:"brands",name:"Brands"},{id:"templates",name:"Templates"},{id:"users",name:"Users"}].map(e=>(0,a.jsx)("button",{onClick:()=>v(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(N===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.name},e.id))})}),"overview"===N&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDC65"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83C\uDFE2"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Active Brands"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:m.filter(e=>e.isActive).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCC4"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Templates"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:h.length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCCA"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"BVAs Created"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.length})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Users"}),(0,a.jsx)("div",{className:"space-y-3",children:p.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.role})]},e.id))})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent BVAs"}),(0,a.jsx)("div",{className:"space-y-3",children:j.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.clientName})]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"in-progress"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.status})]},e.id))})]})})]})]}),"brands"===N&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Brands Management"}),(0,a.jsx)("button",{onClick:()=>A(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Add New Brand"})]}),w&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Brand"}),(0,a.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Brand Name *"}),(0,a.jsx)("input",{type:"text",value:k.name,onChange:e=>D({...k,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:k.description,onChange:e=>D({...k,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo URL"}),(0,a.jsx)("input",{type:"url",value:k.logoUrl,onChange:e=>D({...k,logoUrl:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"submit",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Create Brand"}),(0,a.jsx)("button",{type:"button",onClick:()=>A(!1),className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400",children:"Cancel"})]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Templates"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.logoUrl&&(0,a.jsx)("img",{className:"h-8 w-8 rounded mr-3",src:e.logoUrl,alt:e.name}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:h.filter(s=>s.brandId===e.id).length}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,a.jsx)("button",{onClick:()=>E(e.id,e.isActive),className:"mr-3 ".concat(e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"),children:e.isActive?"Deactivate":"Activate"}),(0,a.jsx)(c(),{href:"/admin/brands/".concat(e.id),className:"text-blue-600 hover:text-blue-900",children:"Edit"})]})]},e.id))})]})})]}),"templates"===N&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Templates Management"}),(0,a.jsx)(c(),{href:"/admin/templates/new",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Upload New Template"})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Template"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(e=>{let s=m.find(s=>s.id===e.brandId);return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(null==s?void 0:s.name)||"Unknown"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.category}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.price]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,a.jsx)("button",{onClick:()=>P(e.id,e.isActive),className:"mr-3 ".concat(e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"),children:e.isActive?"Deactivate":"Activate"}),(0,a.jsx)(c(),{href:"/admin/templates/".concat(e.id),className:"text-blue-600 hover:text-blue-900",children:"Edit"})]})]},e.id)})})]})})]}),"users"===N&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Users management coming soon..."})})]})})]}):null}},7603:(e,s,t)=>{Promise.resolve().then(t.bind(t,7161))}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,288,874,441,684,358],()=>s(7603)),_N_E=e.O()}]);