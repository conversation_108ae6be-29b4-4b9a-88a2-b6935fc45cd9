exports.id=798,exports.ids=[798],exports.modules={3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},5481:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(60687),a=r(85814),o=r.n(a),i=r(30474),n=r(51108),l=r(16189),d=r(27436);function c({title:e="VALTICS AI",showBackButton:t=!1,backUrl:r="/dashboard",backText:a="← Back to Dashboard"}){let{user:c,logOut:u,isAdmin:h}=(0,n.A)(),m=(0,l.useRouter)(),v=async()=>{try{await u(),m.push("/")}catch(e){console.error("Error logging out:",e)}};return c?(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)(o(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,s.jsx)(i.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,s.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&(0,s.jsx)(o(),{href:r,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:a}),!t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,s.jsx)(o(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),h&&(0,s.jsx)(o(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,s.jsx)(o(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,s.jsx)(d.default,{}),(0,s.jsx)("button",{onClick:v,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27436:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),a=r(40303),o=r(43210);function i(){let[e,t]=(0,o.useState)(!1),[r,i]=(0,o.useState)("light"),n=(0,a.Q)(),l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};if(!e)return(0,s.jsx)("div",{className:"p-2 w-9 h-9"});let d=n?n.theme:r;return(0,s.jsx)("button",{onClick:()=>{if(n)n.toggleTheme();else{let e="light"===r?"dark":"light";i(e),l(e),localStorage.setItem("theme",e)}},className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":`Switch to ${"light"===d?"dark":"light"} mode`,title:`Switch to ${"light"===d?"dark":"light"} mode`,children:"light"===d?(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}},30474:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(31261),a=r.n(s)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return n}});let s=r(37366),a=r(44953),o=r(46533),i=s._(r(1933));function n(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=o.Image},40303:(e,t,r)=>{"use strict";r.d(t,{Q:()=>i,ThemeProvider:()=>n});var s=r(60687),a=r(43210);let o=(0,a.createContext)(null),i=()=>(0,a.useContext)(o),n=({children:e})=>{let[t,r]=(0,a.useState)("light"),[i,n]=(0,a.useState)(!1);(0,a.useEffect)(()=>{n(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return i?(0,s.jsx)(o.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>d});var s=r(60687),a=r(43210),o=r(19978),i=r(56304),n=r(75535);let l=(0,a.createContext)(null),d=({children:e})=>{let[t,r]=(0,a.useState)(null),[d,c]=(0,a.useState)(!0),[u,h]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=(0,o.hg)(i.j2,async e=>{if(r(e),e){let t=await (0,n.x7)((0,n.H9)(i.db,"users",e.uid));h(t.data()?.role==="admin")}else h(!1);c(!1)});return()=>e()},[]);let m=async(e,t)=>{await (0,o.x9)(i.j2,e,t)},v=async(e,t)=>{let r=await (0,o.eJ)(i.j2,e,t);await (0,n.BN)((0,n.H9)(i.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},x=async()=>{await (0,o.CI)(i.j2)},f=async()=>{let e=new o.HF,t=await (0,o.df)(i.j2,e);(await (0,n.x7)((0,n.H9)(i.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(i.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},p=async()=>{let e=new o.sk,t=await (0,o.df)(i.j2,e);(await (0,n.x7)((0,n.H9)(i.db,"users",t.user.uid))).exists()||await (0,n.BN)((0,n.H9)(i.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(l.Provider,{value:{user:t,loading:d,signIn:m,signUp:v,logOut:x,signInWithGoogle:f,signInWithFacebook:p,isAdmin:u},children:e})},c=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>l});var s=r(67989),a=r(19978),o=r(75535),i=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,a.xI)(n),d=(0,o.aU)(n);(0,i.c7)(n)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),a=r(61421),o=r.n(a);r(82704);var i=r(94442),n=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:o().className,children:(0,s.jsx)(n.ThemeProvider,{children:(0,s.jsx)(i.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},82704:()=>{},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")}};