(()=>{var e={};e.id=907,e.ids=[907],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},4836:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19500)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\pricing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\pricing\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/pricing/page",pathname:"/pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5566:(e,t,r)=>{Promise.resolve().then(r.bind(r,19500))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19500:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\pricing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\pricing\\page.tsx","default")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27436:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(40303),i=r(43210);function n(){let[e,t]=(0,i.useState)(!1),[r,n]=(0,i.useState)("light"),o=(0,a.Q)(),l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};if(!e)return(0,s.jsx)("div",{className:"p-2 w-9 h-9"});let d=o?o.theme:r;return(0,s.jsx)("button",{onClick:()=>{if(o)o.toggleTheme();else{let e="light"===r?"dark":"light";n(e),l(e),localStorage.setItem("theme",e)}},className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":`Switch to ${"light"===d?"dark":"light"} mode`,title:`Switch to ${"light"===d?"dark":"light"} mode`,children:"light"===d?(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,ThemeProvider:()=>o});var s=r(60687),a=r(43210);let i=(0,a.createContext)(null),n=()=>(0,a.useContext)(i),o=({children:e})=>{let[t,r]=(0,a.useState)("light"),[n,o]=(0,a.useState)(!1);(0,a.useEffect)(()=>{o(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return n?(0,s.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>d});var s=r(60687),a=r(43210),i=r(19978),n=r(56304),o=r(75535);let l=(0,a.createContext)(null),d=({children:e})=>{let[t,r]=(0,a.useState)(null),[d,c]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=(0,i.hg)(n.j2,async e=>{if(r(e),e){let t=await (0,o.x7)((0,o.H9)(n.db,"users",e.uid));m(t.data()?.role==="admin")}else m(!1);c(!1)});return()=>e()},[]);let h=async(e,t)=>{await (0,i.x9)(n.j2,e,t)},x=async(e,t)=>{let r=await (0,i.eJ)(n.j2,e,t);await (0,o.BN)((0,o.H9)(n.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},p=async()=>{await (0,i.CI)(n.j2)},g=async()=>{let e=new i.HF,t=await (0,i.df)(n.j2,e);(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()||await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},v=async()=>{let e=new i.sk,t=await (0,i.df)(n.j2,e);(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()||await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(l.Provider,{value:{user:t,loading:d,signIn:h,signUp:x,logOut:p,signInWithGoogle:g,signInWithFacebook:v,isAdmin:u},children:e})},c=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>l});var s=r(67989),a=r(19978),i=r(75535),n=r(70146);let o=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),l=(0,a.xI)(o),d=(0,i.aU)(o);(0,n.c7)(o)},57034:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),a=r(51108),i=r(85814),n=r.n(i),o=r(27436);function l(){let{user:e}=(0,a.A)();return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(n(),{href:e?"/dashboard":"/",className:"text-xl font-semibold text-gray-900 dark:text-white",children:"VALTICS AI"})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:e?(0,s.jsx)(n(),{href:"/dashboard",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Dashboard"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n(),{href:"/login",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Login"}),(0,s.jsx)(o.default,{}),(0,s.jsx)(n(),{href:"/register",className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Sign Up"})]})})]})})}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto py-12 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Choose Your Plan"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Select the perfect plan for your business value analysis needs. All plans include access to our AI-powered BVA platform."})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:[{name:"Basic",price:29,period:"month",description:"Perfect for small teams getting started with BVA",features:["Up to 5 BVAs per month","Access to basic templates","Standard support","PDF exports","Basic analytics"],buttonText:"Start Basic Plan",popular:!1},{name:"Premium",price:79,period:"month",description:"Ideal for growing businesses with advanced needs",features:["Unlimited BVAs","Access to all templates","Priority support","PDF & PowerPoint exports","Advanced analytics","Custom branding","API access"],buttonText:"Start Premium Plan",popular:!0},{name:"Enterprise",price:199,period:"month",description:"For large organizations with complex requirements",features:["Everything in Premium","Custom templates","Dedicated account manager","SSO integration","Advanced security","Custom integrations","Training & onboarding"],buttonText:"Contact Sales",popular:!1}].map((e,t)=>(0,s.jsxs)("div",{className:`relative bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden ${e.popular?"ring-2 ring-blue-500 dark:ring-blue-400":""}`,children:[e.popular&&(0,s.jsx)("div",{className:"absolute top-0 left-0 right-0 bg-blue-500 dark:bg-blue-600 text-white text-center py-2 text-sm font-medium",children:"Most Popular"}),(0,s.jsxs)("div",{className:`p-8 ${e.popular?"pt-12":""}`,children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:e.name}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:e.description}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("span",{className:"text-4xl font-bold text-gray-900 dark:text-white",children:["$",e.price]}),(0,s.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:["/",e.period]})]}),(0,s.jsx)("ul",{className:"space-y-3 mb-8",children:e.features.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-5 h-5 bg-green-500 dark:bg-green-600 rounded-full flex items-center justify-center mr-3 mt-0.5",children:(0,s.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:e})]},t))}),(0,s.jsx)("button",{className:`w-full py-3 px-4 rounded-md font-medium transition-colors ${e.popular?"bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-800":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.buttonText})]})]},e.name))}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center",children:"Frequently Asked Questions"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Can I change my plan later?"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Is there a free trial?"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"We offer a 14-day free trial for all new users. No credit card required to get started."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"What payment methods do you accept?"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"We accept all major credit cards, PayPal, and bank transfers for enterprise customers."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Can I cancel anytime?"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Do you offer custom templates?"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Custom templates are available for Enterprise customers. Contact our sales team for more information."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Is my data secure?"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Yes, we use enterprise-grade security measures including encryption, secure data centers, and regular security audits."})]})]})]}),(0,s.jsxs)("div",{className:"text-center mt-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Ready to Get Started?"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-8",children:"Join thousands of businesses already using VALTICS AI to create compelling business value analyses."}),(0,s.jsxs)("div",{className:"flex justify-center space-x-4",children:[!e&&(0,s.jsx)(n(),{href:"/register",className:"bg-blue-600 dark:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Start Free Trial"}),(0,s.jsx)("button",{className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Contact Sales"})]})]})]})})]})}},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),a=r(61421),i=r.n(a);r(82704);var n=r(94442),o=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:i().className,children:(0,s.jsx)(o.ThemeProvider,{children:(0,s.jsx)(n.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")},97318:(e,t,r)=>{Promise.resolve().then(r.bind(r,57034))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,567],()=>r(4836));module.exports=s})();