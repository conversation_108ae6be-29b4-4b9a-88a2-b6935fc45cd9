{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/brands/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { collection, query, where, getDocs, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Brand, Template } from '@/types';\n\nexport default function Brands() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [brands, setBrands] = useState<Brand[]>([]);\n  const [templates, setTemplates] = useState<Record<string, Template[]>>({});\n  const [loadingData, setLoadingData] = useState(true);\n  const [selectedBrand, setSelectedBrand] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user) {\n      fetchBrandsData();\n    }\n  }, [user]);\n\n  const fetchBrandsData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch active brands\n      const brandsQuery = query(\n        collection(db, 'brands'),\n        where('isActive', '==', true),\n        orderBy('name', 'asc')\n      );\n      const brandsSnapshot = await getDocs(brandsQuery);\n      const brandsData = brandsSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Brand[];\n      setBrands(brandsData);\n\n      // Fetch templates for each brand\n      const templatesData: Record<string, Template[]> = {};\n      for (const brand of brandsData) {\n        const templatesQuery = query(\n          collection(db, 'templates'),\n          where('brandId', '==', brand.id),\n          where('isActive', '==', true),\n          orderBy('name', 'asc')\n        );\n        const templatesSnapshot = await getDocs(templatesQuery);\n        templatesData[brand.id] = templatesSnapshot.docs.map(doc => ({\n          id: doc.id,\n          ...doc.data()\n        })) as Template[];\n      }\n      setTemplates(templatesData);\n\n    } catch (error) {\n      console.error('Error fetching brands data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-xl font-semibold text-gray-900\">\n                VALTICS AI\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Dashboard\n              </Link>\n              <Link\n                href=\"/templates\"\n                className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Templates\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Solution Brands</h1>\n            <p className=\"mt-2 text-gray-600\">\n              Explore our comprehensive collection of technology solution brands and their BVA templates.\n            </p>\n          </div>\n\n          {brands.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {brands.map((brand) => (\n                <div key={brand.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                  <div className=\"p-6\">\n                    {brand.logoUrl && (\n                      <div className=\"mb-4\">\n                        <img\n                          src={brand.logoUrl}\n                          alt={`${brand.name} logo`}\n                          className=\"h-12 w-auto\"\n                        />\n                      </div>\n                    )}\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                      {brand.name}\n                    </h3>\n                    <p className=\"text-gray-600 mb-4\">\n                      {brand.description}\n                    </p>\n                    \n                    {/* Templates count */}\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <span className=\"text-sm text-gray-500\">\n                        {templates[brand.id]?.length || 0} templates available\n                      </span>\n                      <button\n                        onClick={() => setSelectedBrand(selectedBrand === brand.id ? null : brand.id)}\n                        className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                      >\n                        {selectedBrand === brand.id ? 'Hide Templates' : 'View Templates'}\n                      </button>\n                    </div>\n\n                    {/* Templates list (collapsible) */}\n                    {selectedBrand === brand.id && templates[brand.id] && (\n                      <div className=\"border-t pt-4\">\n                        <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Available Templates:</h4>\n                        <div className=\"space-y-2\">\n                          {templates[brand.id].map((template) => (\n                            <div key={template.id} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                              <div>\n                                <p className=\"text-sm font-medium text-gray-900\">{template.name}</p>\n                                <p className=\"text-xs text-gray-500\">{template.category}</p>\n                              </div>\n                              <div className=\"flex items-center space-x-2\">\n                                <span className=\"text-sm font-medium text-green-600\">${template.price}</span>\n                                <Link\n                                  href={`/templates/${template.id}`}\n                                  className=\"bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-blue-700\"\n                                >\n                                  Use\n                                </Link>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* View all templates button */}\n                    <div className=\"mt-4\">\n                      <Link\n                        href={`/templates?brand=${brand.id}`}\n                        className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 inline-block text-center\"\n                      >\n                        Explore {brand.name} Templates\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 text-6xl mb-4\">📦</div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No brands available</h3>\n              <p className=\"text-gray-600\">\n                Check back later for new solution brands and templates.\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,kBAAkB;QACtB,IAAI;YACF,eAAe;YAEf,sBAAsB;YACtB,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACtB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;YAElB,MAAM,iBAAiB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACrC,MAAM,aAAa,eAAe,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACjD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,UAAU;YAEV,iCAAiC;YACjC,MAAM,gBAA4C,CAAC;YACnD,KAAK,MAAM,SAAS,WAAY;gBAC9B,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACzB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,MAAM,EAAE,GAC/B,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;gBAElB,MAAM,oBAAoB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;gBACxC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,kBAAkB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC3D,IAAI,IAAI,EAAE;wBACV,GAAG,IAAI,IAAI,EAAE;oBACf,CAAC;YACH;YACA,aAAa;QAEf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAsC;;;;;;;;;;;0CAI1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;wBAKnC,OAAO,MAAM,GAAG,kBACf,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oCAAmB,WAAU;8CAC5B,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,MAAM,OAAO,kBACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK,MAAM,OAAO;oDAClB,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;oDACzB,WAAU;;;;;;;;;;;0DAIhB,8OAAC;gDAAG,WAAU;0DACX,MAAM,IAAI;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;0DAIpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,UAAU;4DAAE;;;;;;;kEAEpC,8OAAC;wDACC,SAAS,IAAM,iBAAiB,kBAAkB,MAAM,EAAE,GAAG,OAAO,MAAM,EAAE;wDAC5E,WAAU;kEAET,kBAAkB,MAAM,EAAE,GAAG,mBAAmB;;;;;;;;;;;;4CAKpD,kBAAkB,MAAM,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC,kBAChD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,yBACxB,8OAAC;gEAAsB,WAAU;;kFAC/B,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAqC,SAAS,IAAI;;;;;;0FAC/D,8OAAC;gFAAE,WAAU;0FAAyB,SAAS,QAAQ;;;;;;;;;;;;kFAEzD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;;oFAAqC;oFAAE,SAAS,KAAK;;;;;;;0FACrE,8OAAC,4JAAA,CAAA,UAAI;gFACH,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;gFACjC,WAAU;0FACX;;;;;;;;;;;;;+DAVK,SAAS,EAAE;;;;;;;;;;;;;;;;0DAqB7B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAE;oDACpC,WAAU;;wDACX;wDACU,MAAM,IAAI;wDAAC;;;;;;;;;;;;;;;;;;mCA/DlB,MAAM,EAAE;;;;;;;;;iDAuEtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}]}