'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import {
  User as FirebaseUser,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  signInWithPopup,
  FacebookAuthProvider
} from 'firebase/auth';
import { auth, db } from '@/lib/firebase/config';
import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { User } from '@/types';
import { createNewUserWithTrial, isTrialExpired, canAccessPremiumFeatures } from '@/lib/trial';

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  logOut: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithFacebook: () => Promise<void>;
  isAdmin: boolean;
  canAccessPremiumFeatures: boolean;
  isTrialExpired: boolean;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [canAccessPremium, setCanAccessPremium] = useState(false);
  const [trialExpired, setTrialExpired] = useState(false);

  const refreshUserData = async () => {
    if (!firebaseUser) return;

    try {
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (userDoc.exists()) {
        const userData = { id: userDoc.id, ...userDoc.data() } as User;

        // Update trial status if needed
        const expired = isTrialExpired(userData);
        if (expired && !userData.trialExpired) {
          await updateDoc(doc(db, 'users', firebaseUser.uid), {
            trialExpired: true,
            updatedAt: new Date()
          });
          userData.trialExpired = true;
        }

        setUser(userData);
        setIsAdmin(userData.role === 'admin');
        setCanAccessPremium(canAccessPremiumFeatures(userData));
        setTrialExpired(expired);
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setFirebaseUser(firebaseUser);

      if (firebaseUser) {
        await refreshUserData();
      } else {
        setUser(null);
        setIsAdmin(false);
        setCanAccessPremium(false);
        setTrialExpired(false);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Update user data when firebaseUser changes
  useEffect(() => {
    if (firebaseUser) {
      refreshUserData();
    }
  }, [firebaseUser]);

  const signIn = async (email: string, password: string) => {
    await signInWithEmailAndPassword(auth, email, password);
  };

  const signUp = async (email: string, password: string) => {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);

    // Create user document in Firestore with trial data
    const newUserData = createNewUserWithTrial(userCredential.user.email || email);
    await setDoc(doc(db, 'users', userCredential.user.uid), newUserData);
  };

  const logOut = async () => {
    await signOut(auth);
  };

  const signInWithGoogle = async () => {
    const provider = new GoogleAuthProvider();
    const userCredential = await signInWithPopup(auth, provider);

    // Check if user exists in Firestore, if not create a new document with trial
    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));

    if (!userDoc.exists()) {
      const newUserData = createNewUserWithTrial(
        userCredential.user.email || '',
        {
          firstName: userCredential.user.displayName?.split(' ')[0],
          lastName: userCredential.user.displayName?.split(' ').slice(1).join(' ')
        }
      );
      await setDoc(doc(db, 'users', userCredential.user.uid), newUserData);
    }
  };

  const signInWithFacebook = async () => {
    const provider = new FacebookAuthProvider();
    const userCredential = await signInWithPopup(auth, provider);

    // Check if user exists in Firestore, if not create a new document with trial
    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));

    if (!userDoc.exists()) {
      const newUserData = createNewUserWithTrial(
        userCredential.user.email || '',
        {
          firstName: userCredential.user.displayName?.split(' ')[0],
          lastName: userCredential.user.displayName?.split(' ').slice(1).join(' ')
        }
      );
      await setDoc(doc(db, 'users', userCredential.user.uid), newUserData);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      firebaseUser,
      loading,
      signIn,
      signUp,
      logOut,
      signInWithGoogle,
      signInWithFacebook,
      isAdmin,
      canAccessPremiumFeatures: canAccessPremium,
      isTrialExpired: trialExpired,
      refreshUserData
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};