{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/pricing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport Link from 'next/link';\n\nexport default function Pricing() {\n  const { user } = useAuth();\n\n  const plans = [\n    {\n      name: 'Basic',\n      price: 29,\n      period: 'month',\n      description: 'Perfect for small teams getting started with BVA',\n      features: [\n        'Up to 5 BVAs per month',\n        'Access to basic templates',\n        'Standard support',\n        'PDF exports',\n        'Basic analytics'\n      ],\n      buttonText: 'Start Basic Plan',\n      popular: false\n    },\n    {\n      name: 'Premium',\n      price: 79,\n      period: 'month',\n      description: 'Ideal for growing businesses with advanced needs',\n      features: [\n        'Unlimited BVAs',\n        'Access to all templates',\n        'Priority support',\n        'PDF & PowerPoint exports',\n        'Advanced analytics',\n        'Custom branding',\n        'API access'\n      ],\n      buttonText: 'Start Premium Plan',\n      popular: true\n    },\n    {\n      name: 'Enterprise',\n      price: 199,\n      period: 'month',\n      description: 'For large organizations with complex requirements',\n      features: [\n        'Everything in Premium',\n        'Custom templates',\n        'Dedicated account manager',\n        'SSO integration',\n        'Advanced security',\n        'Custom integrations',\n        'Training & onboarding'\n      ],\n      buttonText: 'Contact Sales',\n      popular: false\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href={user ? \"/dashboard\" : \"/\"} className=\"text-xl font-semibold text-gray-900\">\n                VALTICS AI\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <Link\n                  href=\"/dashboard\"\n                  className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Dashboard\n                </Link>\n              ) : (\n                <>\n                  <Link\n                    href=\"/login\"\n                    className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    href=\"/register\"\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700\"\n                  >\n                    Sign Up\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-12 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              Choose Your Plan\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Select the perfect plan for your business value analysis needs. \n              All plans include access to our AI-powered BVA platform.\n            </p>\n          </div>\n\n          {/* Pricing Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n            {plans.map((plan, index) => (\n              <div\n                key={plan.name}\n                className={`relative bg-white rounded-lg shadow-lg overflow-hidden ${\n                  plan.popular ? 'ring-2 ring-blue-500' : ''\n                }`}\n              >\n                {plan.popular && (\n                  <div className=\"absolute top-0 left-0 right-0 bg-blue-500 text-white text-center py-2 text-sm font-medium\">\n                    Most Popular\n                  </div>\n                )}\n                \n                <div className={`p-8 ${plan.popular ? 'pt-12' : ''}`}>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{plan.name}</h3>\n                  <p className=\"text-gray-600 mb-6\">{plan.description}</p>\n                  \n                  <div className=\"mb-6\">\n                    <span className=\"text-4xl font-bold text-gray-900\">${plan.price}</span>\n                    <span className=\"text-gray-600\">/{plan.period}</span>\n                  </div>\n\n                  <ul className=\"space-y-3 mb-8\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-start\">\n                        <div className=\"flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                          <span className=\"text-white text-xs\">✓</span>\n                        </div>\n                        <span className=\"text-gray-700\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  <button\n                    className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${\n                      plan.popular\n                        ? 'bg-blue-600 text-white hover:bg-blue-700'\n                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200'\n                    }`}\n                  >\n                    {plan.buttonText}\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* FAQ Section */}\n          <div className=\"bg-white rounded-lg shadow-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">\n              Frequently Asked Questions\n            </h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Can I change my plan later?\n                </h3>\n                <p className=\"text-gray-600\">\n                  Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Is there a free trial?\n                </h3>\n                <p className=\"text-gray-600\">\n                  We offer a 14-day free trial for all new users. No credit card required to get started.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  What payment methods do you accept?\n                </h3>\n                <p className=\"text-gray-600\">\n                  We accept all major credit cards, PayPal, and bank transfers for enterprise customers.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Can I cancel anytime?\n                </h3>\n                <p className=\"text-gray-600\">\n                  Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Do you offer custom templates?\n                </h3>\n                <p className=\"text-gray-600\">\n                  Custom templates are available for Enterprise customers. Contact our sales team for more information.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Is my data secure?\n                </h3>\n                <p className=\"text-gray-600\">\n                  Yes, we use enterprise-grade security measures including encryption, secure data centers, and regular security audits.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* CTA Section */}\n          <div className=\"text-center mt-16\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Ready to Get Started?\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              Join thousands of businesses already using VALTICS AI to create compelling business value analyses.\n            </p>\n            <div className=\"flex justify-center space-x-4\">\n              {!user && (\n                <Link\n                  href=\"/register\"\n                  className=\"bg-blue-600 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700\"\n                >\n                  Start Free Trial\n                </Link>\n              )}\n              <button className=\"bg-gray-200 text-gray-800 px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-300\">\n                Contact Sales\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,OAAO,eAAe;oCAAK,WAAU;8CAAsC;;;;;;;;;;;0CAIzF,8OAAC;gCAAI,WAAU;0CACZ,qBACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;yDAID;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAOzD,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAEC,WAAW,CAAC,uDAAuD,EACjE,KAAK,OAAO,GAAG,yBAAyB,IACxC;;wCAED,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;sDAA4F;;;;;;sDAK7G,8OAAC;4CAAI,WAAW,CAAC,IAAI,EAAE,KAAK,OAAO,GAAG,UAAU,IAAI;;8DAClD,8OAAC;oDAAG,WAAU;8DAAyC,KAAK,IAAI;;;;;;8DAChE,8OAAC;oDAAE,WAAU;8DAAsB,KAAK,WAAW;;;;;;8DAEnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAmC;gEAAE,KAAK,KAAK;;;;;;;sEAC/D,8OAAC;4DAAK,WAAU;;gEAAgB;gEAAE,KAAK,MAAM;;;;;;;;;;;;;8DAG/C,8OAAC;oDAAG,WAAU;8DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;4DAAsB,WAAU;;8EAC/B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;8EAEvC,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;;2DAJ1B;;;;;;;;;;8DASb,8OAAC;oDACC,WAAW,CAAC,0DAA0D,EACpE,KAAK,OAAO,GACR,6CACA,+CACJ;8DAED,KAAK,UAAU;;;;;;;;;;;;;mCAtCf,KAAK,IAAI;;;;;;;;;;sCA8CpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAIlE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAQnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,sBACA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAIH,8OAAC;4CAAO,WAAU;sDAAuF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvH", "debugId": null}}]}