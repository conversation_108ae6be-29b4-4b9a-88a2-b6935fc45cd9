(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},15667:(e,t,r)=>{Promise.resolve().then(r.bind(r,63393))},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},17199:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\profile\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,t,r)=>{"use strict";r.d(t,{D:()=>l,ThemeProvider:()=>d});var s=r(60687),a=r(43210);let i=(0,a.createContext)(null),d=({children:e})=>{let[t,r]=(0,a.useState)("light"),[d,l]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{let e,t=localStorage.getItem("valtics-theme");e=t||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");let s=document.documentElement;"dark"===e?s.classList.add("dark"):s.classList.remove("dark"),r(e),l(!0)},[]),(0,a.useEffect)(()=>{if(d){let e=document.documentElement;"dark"===t?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("valtics-theme",t)}},[t,d]),d)?(0,s.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{r(e=>"light"===e?"dark":"light")},setTheme:e=>{r(e)}},children:e}):(0,s.jsx)("div",{className:"min-h-screen bg-white",children:e})},l=()=>{let e=(0,a.useContext)(i);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>o});var s=r(60687),a=r(43210),i=r(19978),d=r(56304),l=r(75535);let n=(0,a.createContext)(null),o=({children:e})=>{let[t,r]=(0,a.useState)(null),[o,c]=(0,a.useState)(!0),[x,m]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=(0,i.hg)(d.j2,async e=>{if(r(e),e){let t=await (0,l.x7)((0,l.H9)(d.db,"users",e.uid));m(t.data()?.role==="admin")}else m(!1);c(!1)});return()=>e()},[]);let u=async(e,t)=>{await (0,i.x9)(d.j2,e,t)},h=async(e,t)=>{let r=await (0,i.eJ)(d.j2,e,t);await (0,l.BN)((0,l.H9)(d.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},p=async()=>{await (0,i.CI)(d.j2)},g=async()=>{let e=new i.HF,t=await (0,i.df)(d.j2,e);(await (0,l.x7)((0,l.H9)(d.db,"users",t.user.uid))).exists()||await (0,l.BN)((0,l.H9)(d.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})},b=async()=>{let e=new i.sk,t=await (0,i.df)(d.j2,e);(await (0,l.x7)((0,l.H9)(d.db,"users",t.user.uid))).exists()||await (0,l.BN)((0,l.H9)(d.db,"users",t.user.uid),{email:t.user.email,role:"user",createdAt:new Date})};return(0,s.jsx)(n.Provider,{value:{user:t,loading:o,signIn:u,signUp:h,logOut:p,signInWithGoogle:g,signInWithFacebook:b,isAdmin:x},children:e})},c=()=>{let e=(0,a.useContext)(n);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>o,j2:()=>n});var s=r(67989),a=r(19978),i=r(75535),d=r(70146);let l=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),n=(0,a.xI)(l),o=(0,i.aU)(l);(0,d.c7)(l)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var s=r(37413),a=r(61421),i=r.n(a);r(82704);var d=r(94442),l=r(3465);let n={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:i().className,children:(0,s.jsx)(l.ThemeProvider,{children:(0,s.jsx)(d.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},59e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=r(65239),a=r(48088),i=r(88170),d=r.n(i),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let o={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,17199)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\profile\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),a=r(43210),i=r(51108),d=r(40303),l=r(16189),n=r(85814),o=r.n(n);function c(){let{user:e,loading:t,logOut:r}=(0,i.A)(),{theme:n,setTheme:c}=(0,d.D)(),x=(0,l.useRouter)(),[m,u]=(0,a.useState)(null),[h,p]=(0,a.useState)(null),[g,b]=(0,a.useState)([]),[v,y]=(0,a.useState)(!0),[f,k]=(0,a.useState)("profile"),[j,N]=(0,a.useState)(!1),w=async()=>{try{await r(),x.push("/")}catch(e){console.error("Error logging out:",e)}};if(t||v)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,s.jsx)("div",{className:"text-xl",children:"Loading..."})});if(!e||!m)return null;let P=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"in-progress":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-slate-900",children:[(0,s.jsx)("nav",{className:"bg-white dark:bg-slate-800 shadow-sm border-b border-gray-200 dark:border-slate-700",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(o(),{href:"/dashboard",className:"text-xl font-semibold text-gray-900 dark:text-white",children:"VALTICS AI"})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsx)(o(),{href:"/dashboard",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"← Back to Dashboard"})})]})})}),(0,s.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Profile & Settings"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Manage your account settings and view your activity."})]}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("nav",{className:"flex space-x-8",children:[{id:"profile",name:"Profile"},{id:"subscription",name:"Subscription"},{id:"bvas",name:"My BVAs"}].map(e=>(0,s.jsx)("button",{onClick:()=>k(e.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${f===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-slate-600"}`,children:e.name},e.id))})}),"profile"===f&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,s.jsx)("input",{type:"email",value:e.email||"",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Email cannot be changed"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Account Type"}),(0,s.jsx)("input",{type:"text",value:"admin"===m.role?"Administrator":"Standard User",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Member Since"}),(0,s.jsx)("input",{type:"text",value:new Date(m.createdAt).toLocaleDateString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Total BVAs Created"}),(0,s.jsx)("input",{type:"text",value:g.length.toString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-gray-50 dark:bg-slate-700 text-gray-500 dark:text-gray-400"})]})]})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Appearance Settings"}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-slate-600 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Theme Preference"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Choose your preferred color scheme"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>c("light"),className:`px-3 py-2 rounded-md text-sm font-medium ${"light"===n?"bg-blue-600 text-white":"bg-gray-200 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600"}`,children:"Light"}),(0,s.jsx)("button",{onClick:()=>c("dark"),className:`px-3 py-2 rounded-md text-sm font-medium ${"dark"===n?"bg-blue-600 text-white":"bg-gray-200 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600"}`,children:"Dark"})]})]})})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Actions"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-slate-600 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Change Password"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Update your account password"})]}),(0,s.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Change Password"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-slate-600 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Download Data"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Export all your BVA data"})]}),(0,s.jsx)("button",{className:"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-800",children:"Export Data"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-red-200 dark:border-red-600 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-red-900 dark:text-red-300",children:"Sign Out"}),(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:"Sign out of your account"})]}),(0,s.jsx)("button",{onClick:w,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Sign Out"})]})]})]})})]}),"subscription"===f&&(0,s.jsx)("div",{className:"space-y-6",children:h?(0,s.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Current Subscription"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Plan Type"}),(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"enterprise"===h.type?"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300":"premium"===h.type?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"}`,children:h.type.charAt(0).toUpperCase()+h.type.slice(1)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),(0,s.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"active"===h.status?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300":"cancelled"===h.status?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"}`,children:h.status.charAt(0).toUpperCase()+h.status.slice(1)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Start Date"}),(0,s.jsx)("p",{className:"text-sm text-gray-900 dark:text-white",children:new Date(h.startDate).toLocaleDateString()})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"End Date"}),(0,s.jsx)("p",{className:"text-sm text-gray-900 dark:text-white",children:new Date(h.endDate).toLocaleDateString()})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Access"}),(0,s.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white",children:[h.accessibleTemplateIds.length," templates available"]})]})]}),(0,s.jsxs)("div",{className:"mt-6 flex space-x-4",children:[(0,s.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Upgrade Plan"}),(0,s.jsx)("button",{className:"bg-gray-300 dark:bg-slate-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400 dark:hover:bg-slate-500",children:"Manage Billing"})]})]})}):(0,s.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-6xl mb-4",children:"\uD83D\uDCB3"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No Active Subscription"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Subscribe to access premium templates and features."}),(0,s.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"View Subscription Plans"})]})})}),"bvas"===f&&(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Your BVAs"}),g.length>0?(0,s.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,s.jsx)("div",{className:"border border-gray-200 dark:border-slate-600 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Client: ",e.clientName]}),(0,s.jsxs)("p",{className:"text-xs text-gray-400 dark:text-gray-500",children:["Last updated: ",new Date(e.updatedAt).toLocaleDateString()]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${P(e.status)}`,children:e.status}),(0,s.jsx)(o(),{href:`/bva/${e.id}`,className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"View"})]})]})},e.id))}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-4xl mb-4",children:"\uD83D\uDCCA"}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No BVAs created yet."}),(0,s.jsx)(o(),{href:"/bva/new",className:"mt-4 inline-block bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Create Your First BVA"})]})]})})})]})})]})}r(75535),r(56304)},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78875:(e,t,r)=>{Promise.resolve().then(r.bind(r,17199))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,567],()=>r(59e3));module.exports=s})();