{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { BVAInstance, Template, Brand } from '@/types';\n\nexport default function Dashboard() {\n  const { user, loading, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n  const [bvaInstances, setBvaInstances] = useState<BVAInstance[]>([]);\n  const [recentTemplates, setRecentTemplates] = useState<Template[]>([]);\n  const [brands, setBrands] = useState<Brand[]>([]);\n  const [loadingData, setLoadingData] = useState(true);\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user) {\n      fetchDashboardData();\n    }\n  }, [user]);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch user's BVA instances\n      const bvaQuery = query(\n        collection(db, 'bvaInstances'),\n        where('userId', '==', user?.uid),\n        orderBy('updatedAt', 'desc'),\n        limit(5)\n      );\n      const bvaSnapshot = await getDocs(bvaQuery);\n      const bvaData = bvaSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as BVAInstance[];\n      setBvaInstances(bvaData);\n\n      // Fetch recent templates\n      const templatesQuery = query(\n        collection(db, 'templates'),\n        where('isActive', '==', true),\n        orderBy('createdAt', 'desc'),\n        limit(6)\n      );\n      const templatesSnapshot = await getDocs(templatesQuery);\n      const templatesData = templatesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Template[];\n      setRecentTemplates(templatesData);\n\n      // Fetch brands\n      const brandsQuery = query(\n        collection(db, 'brands'),\n        where('isActive', '==', true)\n      );\n      const brandsSnapshot = await getDocs(brandsQuery);\n      const brandsData = brandsSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Brand[];\n      setBrands(brandsData);\n\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'in-progress':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'draft':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-slate-900\">\n      {/* Navigation */}\n      <nav className=\"bg-white dark:bg-slate-800 shadow-sm border-b border-gray-200 dark:border-slate-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-semibold text-gray-900 dark:text-white\">VALTICS AI</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/brands\"\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Brands\n              </Link>\n              <Link\n                href=\"/templates\"\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Templates\n              </Link>\n              {isAdmin && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Admin\n                </Link>\n              )}\n              <Link\n                href=\"/profile\"\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Profile\n              </Link>\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        {/* Welcome Section */}\n        <div className=\"px-4 py-6 sm:px-0\">\n          <div className=\"bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                Welcome back, {user.email}!\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n                Ready to create powerful Business Value Analysis reports?\n              </p>\n              <Link\n                href=\"/bva/new\"\n                className=\"bg-blue-600 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 inline-block\"\n              >\n                Create New BVA\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">BVA</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                      Total BVAs\n                    </dt>\n                    <dd className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                      {bvaInstances.length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">✓</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                      Completed\n                    </dt>\n                    <dd className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                      {bvaInstances.filter(bva => bva.status === 'completed').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">⏳</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                      In Progress\n                    </dt>\n                    <dd className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                      {bvaInstances.filter(bva => bva.status === 'in-progress').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent BVAs and Templates Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent BVAs */}\n          <div className=\"bg-white dark:bg-slate-800 shadow rounded-lg\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4\">\n                Recent BVAs\n              </h3>\n              {bvaInstances.length > 0 ? (\n                <div className=\"space-y-3\">\n                  {bvaInstances.map((bva) => (\n                    <div key={bva.id} className=\"flex items-center justify-between p-3 border border-gray-200 dark:border-slate-600 rounded-lg\">\n                      <div>\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">{bva.name}</h4>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">{bva.clientName}</p>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(bva.status)}`}>\n                          {bva.status}\n                        </span>\n                        <Link\n                          href={`/bva/${bva.id}`}\n                          className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium\"\n                        >\n                          View\n                        </Link>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-500 dark:text-gray-400 text-center py-4\">\n                  No BVAs created yet. <Link href=\"/bva/new\" className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\">Create your first one!</Link>\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* Available Templates */}\n          <div className=\"bg-white dark:bg-slate-800 shadow rounded-lg\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4\">\n                Available Templates\n              </h3>\n              {recentTemplates.length > 0 ? (\n                <div className=\"space-y-3\">\n                  {recentTemplates.slice(0, 4).map((template) => (\n                    <div key={template.id} className=\"flex items-center justify-between p-3 border border-gray-200 dark:border-slate-600 rounded-lg\">\n                      <div>\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">{template.name}</h4>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">{template.category}</p>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-sm font-medium text-green-600 dark:text-green-400\">${template.price}</span>\n                        <Link\n                          href={`/templates/${template.id}`}\n                          className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium\"\n                        >\n                          Use\n                        </Link>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-500 dark:text-gray-400 text-center py-4\">No templates available</p>\n              )}\n              <div className=\"mt-4\">\n                <Link\n                  href=\"/templates\"\n                  className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium\"\n                >\n                  View all templates →\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,qBAAqB;QACzB,IAAI;YACF,eAAe;YAEf,6BAA6B;YAC7B,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACnB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,iBACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,MAAM,MAC5B,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,SACrB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;YAER,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YAClC,MAAM,UAAU,YAAY,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC3C,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,gBAAgB;YAEhB,yBAAyB;YACzB,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACzB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,SACrB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;YAER,MAAM,oBAAoB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACxC,MAAM,gBAAgB,kBAAkB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACvD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,mBAAmB;YAEnB,eAAe;YACf,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACtB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM;YAE1B,MAAM,iBAAiB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACrC,MAAM,aAAa,eAAe,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACjD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,UAAU;QAEZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;;;;;;0CAEtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwD;4CACrD,KAAK,KAAK;4CAAC;;;;;;;kDAE5B,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;0DAGrD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAG,WAAU;sEACX,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;0DAGrD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAG,WAAU;sEACX,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ1E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;0DAGrD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAG,WAAU;sEACX,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU9E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;wCAGhF,aAAa,MAAM,GAAG,kBACrB,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAqD,IAAI,IAAI;;;;;;8EAC3E,8OAAC;oEAAE,WAAU;8EAA4C,IAAI,UAAU;;;;;;;;;;;;sEAEzE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,IAAI,MAAM,GAAG;8EACxF,IAAI,MAAM;;;;;;8EAEb,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;oEACtB,WAAU;8EACX;;;;;;;;;;;;;mDAZK,IAAI,EAAE;;;;;;;;;iEAoBpB,8OAAC;4CAAE,WAAU;;gDAAoD;8DAC1C,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAgF;;;;;;;;;;;;;;;;;;;;;;;0CAO7I,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmE;;;;;;wCAGhF,gBAAgB,MAAM,GAAG,kBACxB,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,8OAAC;oDAAsB,WAAU;;sEAC/B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAqD,SAAS,IAAI;;;;;;8EAChF,8OAAC;oEAAE,WAAU;8EAA4C,SAAS,QAAQ;;;;;;;;;;;;sEAE5E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;wEAAyD;wEAAE,SAAS,KAAK;;;;;;;8EACzF,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;oEACjC,WAAU;8EACX;;;;;;;;;;;;;mDAVK,SAAS,EAAE;;;;;;;;;iEAkBzB,8OAAC;4CAAE,WAAU;sDAAoD;;;;;;sDAEnE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}