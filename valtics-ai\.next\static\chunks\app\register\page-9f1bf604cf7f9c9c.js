(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{1138:(e,r,a)=>{"use strict";a.d(r,{db:()=>u,j2:()=>o});var t=a(3915),s=a(6203),d=a(5317),i=a(858);let l=(0,t.Dk)().length?(0,t.Sx)():(0,t.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),o=(0,s.xI)(l),u=(0,d.aU)(l);(0,i.c7)(l)},1851:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>u});var t=a(5155),s=a(2115),d=a(3274),i=a(5695),l=a(6874),o=a.n(l);function u(){let[e,r]=(0,s.useState)(""),[a,l]=(0,s.useState)(""),[u,n]=(0,s.useState)(""),[c,m]=(0,s.useState)(""),{signUp:b,signInWithGoogle:g,signInWithFacebook:x}=(0,d.A)(),h=(0,i.useRouter)(),y=async r=>{if(r.preventDefault(),m(""),a!==u)return void m("Passwords do not match");try{await b(e,a),h.push("/dashboard")}catch(e){m(e.message)}},f=async()=>{try{await g(),h.push("/dashboard")}catch(e){m(e.message)}},w=async()=>{try{await x(),h.push("/dashboard")}catch(e){m(e.message)}};return(0,t.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24 bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-md",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Register for VALTICS AI"})}),c&&(0,t.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:c}),(0,t.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:a,onChange:e=>l(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm Password"}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:u,onChange:e=>n(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Register"})})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400",children:"Or continue with"})})]}),(0,t.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,t.jsx)("button",{onClick:f,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Google"}),(0,t.jsx)("button",{onClick:w,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Facebook"})]})]}),(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Already have an account?"," ",(0,t.jsx)(o(),{href:"/login",className:"font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300",children:"Login"})]})})]})})}},3274:(e,r,a)=>{"use strict";a.d(r,{A:()=>n,AuthProvider:()=>u});var t=a(5155),s=a(2115),d=a(6203),i=a(1138),l=a(5317);let o=(0,s.createContext)(null),u=e=>{let{children:r}=e,[a,u]=(0,s.useState)(null),[n,c]=(0,s.useState)(!0),[m,b]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=(0,d.hg)(i.j2,async e=>{if(u(e),e){var r;b((null==(r=(await (0,l.x7)((0,l.H9)(i.db,"users",e.uid))).data())?void 0:r.role)==="admin")}else b(!1);c(!1)});return()=>e()},[]);let g=async(e,r)=>{await (0,d.x9)(i.j2,e,r)},x=async(e,r)=>{let a=await (0,d.eJ)(i.j2,e,r);await (0,l.BN)((0,l.H9)(i.db,"users",a.user.uid),{email:a.user.email,role:"user",createdAt:new Date})},h=async()=>{await (0,d.CI)(i.j2)},y=async()=>{let e=new d.HF,r=await (0,d.df)(i.j2,e);(await (0,l.x7)((0,l.H9)(i.db,"users",r.user.uid))).exists()||await (0,l.BN)((0,l.H9)(i.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})},f=async()=>{let e=new d.sk,r=await (0,d.df)(i.j2,e);(await (0,l.x7)((0,l.H9)(i.db,"users",r.user.uid))).exists()||await (0,l.BN)((0,l.H9)(i.db,"users",r.user.uid),{email:r.user.email,role:"user",createdAt:new Date})};return(0,t.jsx)(o.Provider,{value:{user:a,loading:n,signIn:g,signUp:x,logOut:h,signInWithGoogle:y,signInWithFacebook:f,isAdmin:m},children:r})},n=()=>{let e=(0,s.useContext)(o);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},5695:(e,r,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},8519:(e,r,a)=>{Promise.resolve().then(a.bind(a,1851))}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,288,874,441,684,358],()=>r(8519)),_N_E=e.O()}]);